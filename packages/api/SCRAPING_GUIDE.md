# 🕷️ Web Scraping Guide

This guide covers the comprehensive web scraping functionality added to the AI Answer Bot API.

## 🚀 Quick Start

### 1. Setup Test Database
```bash
cd packages/api
npm run setup-test-db
```

This will:
- Run database migrations to add new scraping fields
- Clear existing data
- Seed with comprehensive test data including:
  - 3 demo clients with different configurations
  - 30+ scraped pages with various statuses
  - Test URLs for scraping functionality
  - Sample analytics data

### 2. Start the API Server
```bash
npm run dev
```

## 📊 Test Data Overview

### Demo Clients Created
1. **Acme Corporation** (`acme.com`)
2. **TechStart Inc** (`techstart.io`) 
3. **OpenSource Docs** (`docs.example.org`)

Each client has:
- Unique API key for authentication
- Sample scraped pages with different statuses
- Bot suggestions
- Analytics data

### Sample Scraped Pages Include
- ✅ **Successful pages**: About, Products, Pricing, Contact, etc.
- ❌ **Failed pages**: 404 errors, maintenance pages
- ⏳ **Pending pages**: Content under review
- 🔄 **Various content types**: Documentation, blogs, legal pages

## 🔧 API Endpoints

### Single URL Scraping
```http
POST /api/clients/{clientId}/scrape
Content-Type: application/json
X-Admin-Key: your_admin_key

{
  "url": "https://example.com",
  "options": {
    "waitForJs": false,
    "timeout": 30000,
    "userAgent": "Custom Bot/1.0",
    "followRedirects": true
  }
}
```

### Bulk URL Scraping
```http
POST /api/clients/{clientId}/scrape/bulk
Content-Type: application/json
X-Admin-Key: your_admin_key

{
  "urls": [
    "https://example.com",
    "https://httpbin.org/html",
    "https://github.com/microsoft/TypeScript"
  ],
  "options": {
    "waitForJs": false,
    "timeout": 30000
  }
}
```

### List Scraped Pages
```http
GET /api/clients/{clientId}/pages?query=search&scrapingStatus=success&limit=20&offset=0
```

### Get Content Statistics
```http
GET /api/clients/{clientId}/pages/stats
```

## 🧪 Test URLs

### ✅ Working URLs (should scrape successfully)
- `https://example.com`
- `https://httpbin.org/html`
- `https://jsonplaceholder.typicode.com`
- `https://github.com/microsoft/TypeScript`
- `https://nodejs.org/en/about`
- `https://developer.mozilla.org/en-US/docs/Web/JavaScript`

### ⚠️ Challenging URLs (may require JavaScript rendering)
- `https://react.dev`
- `https://vuejs.org`
- `https://angular.io`

### ❌ URLs that should fail (for error testing)
- `https://httpbin.org/status/404`
- `https://httpbin.org/status/500`
- `https://httpbin.org/delay/30`
- `https://invalid-domain-that-does-not-exist.com`

### 🔒 URLs requiring special handling
- `https://httpbin.org/robots.txt`
- `https://httpbin.org/redirect/3`
- `https://httpbin.org/gzip`

## 🔍 Features

### Content Extraction
- **Clean Content**: Uses Mozilla Readability for article extraction
- **Metadata**: Extracts titles, descriptions, thumbnails, language
- **Structured Data**: Parses JSON-LD and meta tags
- **Content Analysis**: Word count, language detection, content hashing

### Error Handling
- **Status Tracking**: success/failed/pending/skipped
- **Error Messages**: Detailed error information for debugging
- **Graceful Failures**: Failed scrapes don't break the system
- **Retry Logic**: Built-in retry mechanisms

### Performance
- **Batch Processing**: Handle multiple URLs efficiently
- **Rate Limiting**: Respectful scraping with delays
- **Caching**: Content hash-based deduplication
- **Indexing**: Optimized database queries

### Search & Analytics
- **Full-text Search**: Search across titles, content, descriptions
- **Filtering**: By status, language, date, client
- **Statistics**: Content metrics and scraping performance
- **Pagination**: Efficient large dataset handling

## 🛠️ Development

### Database Schema
New fields added to `scraped_pages` table:
- `scraping_status`: Track scraping success/failure
- `error_message`: Store error details
- `content_hash`: Detect content changes
- `word_count`: Content length metrics
- `language`: Content language
- `metadata`: Flexible JSON storage

### Models & Services
- **ScrapedPageModel**: Complete data access layer
- **ScraperService**: Core scraping functionality
- **Client Routes**: RESTful API endpoints

### Testing
```bash
# Setup test database
npm run setup-test-db

# Run API server
npm run dev

# Test scraping endpoints using curl or Postman
curl -X POST http://localhost:5000/api/clients/{clientId}/scrape \
  -H "Content-Type: application/json" \
  -H "X-Admin-Key: your_admin_key" \
  -d '{"url": "https://example.com"}'
```

## 📈 Monitoring

### Content Statistics
- Total pages scraped
- Success/failure rates
- Content by language
- Word count analytics

### Performance Metrics
- Scraping speed
- Error rates by URL pattern
- Content freshness
- Storage usage

## 🔐 Security

### Rate Limiting
- Built-in delays between requests
- Batch size limits
- Timeout protection

### Content Validation
- URL format validation
- File type restrictions
- Content size limits
- Malicious content detection

### Authentication
- Admin key required for scraping operations
- Client-specific access control
- API key validation

## 🚨 Troubleshooting

### Common Issues
1. **Timeout errors**: Increase timeout in options
2. **JavaScript-heavy sites**: Set `waitForJs: true`
3. **Rate limiting**: Add delays between requests
4. **Large content**: Check size limits

### Debug Information
- Check `error_message` field in scraped_pages
- Monitor server logs for detailed errors
- Use test URLs to verify functionality
- Check database connectivity

## 📚 Next Steps

1. **Test the scraping endpoints** with provided test URLs
2. **Monitor performance** using the statistics endpoints
3. **Customize scraping options** for specific use cases
4. **Integrate with AI responses** for better bot answers
5. **Set up monitoring** for production deployments
