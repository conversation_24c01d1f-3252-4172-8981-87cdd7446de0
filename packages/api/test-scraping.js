/**
 * Quick test script for scraping functionality
 * Run this after setting up the test database to verify everything works
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';
const ADMIN_KEY = process.env.ADMIN_API_KEY || 'your_admin_key_here';

// Test client IDs from the seeded data (these will be different each time you run setup)
const TEST_CLIENT_ID = '91f825e3-9197-47f7-807c-1efba8dfe28e'; // Update this with actual client ID from setup output

async function testScrapingEndpoints() {
  console.log('🧪 Testing Scraping Functionality');
  console.log('==================================');
  
  try {
    // Test 1: Get client pages
    console.log('\n1. 📋 Getting existing scraped pages...');
    const pagesResponse = await axios.get(`${API_BASE}/clients/${TEST_CLIENT_ID}/pages`, {
      headers: { 'X-Admin-Key': ADMIN_KEY }
    });
    console.log(`✅ Found ${pagesResponse.data.pages.length} existing pages`);
    console.log(`   Total pages: ${pagesResponse.data.pagination.total}`);
    
    // Test 2: Get content statistics
    console.log('\n2. 📊 Getting content statistics...');
    const statsResponse = await axios.get(`${API_BASE}/clients/${TEST_CLIENT_ID}/pages/stats`, {
      headers: { 'X-Admin-Key': ADMIN_KEY }
    });
    console.log('✅ Content statistics:');
    console.log(`   Total: ${statsResponse.data.total}`);
    console.log(`   Active: ${statsResponse.data.active}`);
    console.log(`   By status:`, statsResponse.data.byStatus);
    console.log(`   Total words: ${statsResponse.data.totalWords}`);
    
    // Test 3: Single URL scraping
    console.log('\n3. 🕷️  Testing single URL scraping...');
    const scrapeResponse = await axios.post(`${API_BASE}/clients/${TEST_CLIENT_ID}/scrape`, {
      url: 'https://example.com',
      options: {
        timeout: 10000,
        waitForJs: false
      }
    }, {
      headers: { 
        'X-Admin-Key': ADMIN_KEY,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Single URL scraping successful:');
    console.log(`   Title: ${scrapeResponse.data.title}`);
    console.log(`   Status: ${scrapeResponse.data.scrapingStatus}`);
    console.log(`   Word count: ${scrapeResponse.data.wordCount}`);
    console.log(`   Language: ${scrapeResponse.data.language}`);
    
    // Test 4: Bulk URL scraping
    console.log('\n4. 📦 Testing bulk URL scraping...');
    const bulkResponse = await axios.post(`${API_BASE}/clients/${TEST_CLIENT_ID}/scrape/bulk`, {
      urls: [
        'https://httpbin.org/html',
        'https://httpbin.org/status/404', // This should fail
        'https://jsonplaceholder.typicode.com'
      ],
      options: {
        timeout: 10000
      }
    }, {
      headers: { 
        'X-Admin-Key': ADMIN_KEY,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Bulk scraping completed:');
    console.log(`   Total URLs: ${bulkResponse.data.summary.total}`);
    console.log(`   Successful: ${bulkResponse.data.summary.successful}`);
    console.log(`   Failed: ${bulkResponse.data.summary.failed}`);
    
    bulkResponse.data.results.forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.url}: ${result.success ? '✅' : '❌'}`);
      if (!result.success) {
        console.log(`      Error: ${result.error}`);
      }
    });
    
    // Test 5: Search functionality
    console.log('\n5. 🔍 Testing search functionality...');
    const searchResponse = await axios.get(`${API_BASE}/clients/${TEST_CLIENT_ID}/pages`, {
      params: {
        query: 'company',
        scrapingStatus: 'success',
        limit: 5
      },
      headers: { 'X-Admin-Key': ADMIN_KEY }
    });
    console.log(`✅ Search found ${searchResponse.data.pages.length} pages matching "company"`);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   • Start the API server: npm run dev');
    console.log('   • Test more URLs using the provided test URLs');
    console.log('   • Check the admin panel for scraped content');
    console.log('   • Try the bot endpoints to see AI responses with scraped content');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure the API server is running (npm run dev)');
    console.log('   • Update TEST_CLIENT_ID with a valid client ID from the setup output');
    console.log('   • Check that ADMIN_API_KEY is set correctly');
    console.log('   • Verify database connection and seeded data');
  }
}

// Helper function to get client IDs from the database
async function getClientIds() {
  console.log('📋 Available client IDs:');
  console.log('Run the setup script and copy a client ID from the output');
  console.log('Then update TEST_CLIENT_ID in this script');
}

if (require.main === module) {
  if (process.argv.includes('--get-clients')) {
    getClientIds();
  } else {
    testScrapingEndpoints();
  }
}

module.exports = { testScrapingEndpoints };
