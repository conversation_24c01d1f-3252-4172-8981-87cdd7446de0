/**
 * ScrapedPage data transformation utilities
 */

import { ScrapedPage } from '../models/ScrapedPage.model';
import { ScrapedPage as ScrapedPageInterface } from '../types';
import { BaseMapper, BaseDTO, mapperUtils } from './common.mapper';

// ScrapedPage DTO interfaces
export interface ScrapedPageDTO extends BaseDTO {
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  lastScraped: string;
  isActive: boolean;
  scrapingStatus: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  contentHash?: string;
  wordCount?: number;
  language?: string;
  metadata?: Record<string, any>;
}

export interface ScrapedPageCreateDTO {
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
}

export interface ScrapedPageUpdateDTO {
  title?: string;
  content?: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
  isActive?: boolean;
}

export interface ScrapedPageResponseDTO extends ScrapedPageDTO {
  contentPreview?: string;
  clientName?: string;
  clientDomain?: string;
}

export interface ScrapedPageListResponseDTO {
  id: string;
  clientId: string;
  url: string;
  title: string;
  description?: string;
  thumbnail?: string;
  lastScraped: string;
  isActive: boolean;
  scrapingStatus: 'pending' | 'success' | 'failed' | 'skipped';
  wordCount?: number;
  language?: string;
  contentPreview?: string;
}

export interface ScrapedPageStatsDTO {
  totalPages: number;
  activePages: number;
  inactivePages: number;
  byStatus: Record<string, number>;
  byLanguage: Record<string, number>;
  totalWords: number;
  averageWordsPerPage: number;
  recentlyScraped: number;
}

// ScrapedPage mapper class
export class ScrapedPageMapper implements BaseMapper<ScrapedPage, ScrapedPageDTO, ScrapedPageResponseDTO> {
  // Convert Sequelize model to DTO
  toDTO(model: ScrapedPage): ScrapedPageDTO {
    return {
      id: model.id,
      clientId: model.clientId,
      url: model.url,
      title: model.title,
      content: model.content,
      description: model.description,
      thumbnail: model.thumbnail,
      lastScraped: mapperUtils.formatTimestamp(model.lastScraped)!,
      isActive: model.isActive,
      createdAt: mapperUtils.formatTimestamp(model.createdAt)!,
      updatedAt: mapperUtils.formatTimestamp(model.updatedAt)!,
      scrapingStatus: model.scrapingStatus,
      errorMessage: model.errorMessage,
      contentHash: model.contentHash,
      wordCount: model.wordCount,
      language: model.language,
      metadata: model.metadata
    };
  }

  // Convert Sequelize model to response DTO (with additional data)
  toResponse(model: ScrapedPage): ScrapedPageResponseDTO {
    const baseDto = this.toDTO(model);
    return {
      ...baseDto,
      contentPreview: this.generateContentPreview(model.content),
      // Additional fields can be populated by the service layer
      clientName: undefined,
      clientDomain: undefined
    };
  }

  // Convert DTO to partial model data for creation/updates
  toModel(dto: ScrapedPageDTO): Partial<ScrapedPage> {
    return mapperUtils.cleanObject({
      id: dto.id,
      clientId: dto.clientId,
      url: dto.url,
      title: dto.title,
      content: dto.content,
      description: dto.description,
      thumbnail: dto.thumbnail,
      lastScraped: mapperUtils.parseTimestamp(dto.lastScraped),
      isActive: dto.isActive,
      createdAt: mapperUtils.parseTimestamp(dto.createdAt),
      updatedAt: mapperUtils.parseTimestamp(dto.updatedAt),
      scrapingStatus: dto.scrapingStatus,
      errorMessage: dto.errorMessage,
      contentHash: dto.contentHash,
      wordCount: dto.wordCount,
      language: dto.language,
      metadata: dto.metadata
    });
  }

  // Convert create request to model data
  fromCreateRequest(request: ScrapedPageCreateDTO): Partial<ScrapedPage> {
    return mapperUtils.cleanObject({
      clientId: request.clientId,
      url: mapperUtils.normalizeUrl(request.url),
      title: request.title,
      content: request.content,
      description: request.description,
      thumbnail: request.thumbnail,
      scrapingStatus: request.scrapingStatus || 'success',
      errorMessage: request.errorMessage,
      language: request.language,
      metadata: request.metadata || {}
    });
  }

  // Convert update request to model data
  fromUpdateRequest(request: ScrapedPageUpdateDTO): Partial<ScrapedPage> {
    return mapperUtils.cleanObject({
      title: request.title,
      content: request.content,
      description: request.description,
      thumbnail: request.thumbnail,
      scrapingStatus: request.scrapingStatus,
      errorMessage: request.errorMessage,
      language: request.language,
      metadata: request.metadata,
      isActive: request.isActive
    });
  }

  // Convert model to list response (minimal data)
  toListResponse(model: ScrapedPage): ScrapedPageListResponseDTO {
    return {
      id: model.id,
      clientId: model.clientId,
      url: model.url,
      title: model.title,
      description: model.description,
      thumbnail: model.thumbnail,
      lastScraped: mapperUtils.formatTimestamp(model.lastScraped)!,
      isActive: model.isActive,
      scrapingStatus: model.scrapingStatus,
      wordCount: model.wordCount,
      language: model.language,
      contentPreview: this.generateContentPreview(model.content)
    };
  }

  // Convert model to interface (for backward compatibility)
  toInterface(model: ScrapedPage): ScrapedPageInterface {
    return {
      id: model.id,
      clientId: model.clientId,
      url: model.url,
      title: model.title,
      content: model.content,
      description: model.description,
      thumbnail: model.thumbnail,
      lastScraped: model.lastScraped,
      isActive: model.isActive,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      scrapingStatus: model.scrapingStatus,
      errorMessage: model.errorMessage,
      contentHash: model.contentHash,
      wordCount: model.wordCount,
      language: model.language,
      metadata: model.metadata
    };
  }

  // Generate content preview
  private generateContentPreview(content: string, maxLength: number = 200): string {
    return mapperUtils.truncateText(content.replace(/\s+/g, ' ').trim(), maxLength);
  }

  // Enhance response with additional data
  enhanceResponse(
    baseResponse: ScrapedPageResponseDTO,
    additionalData: {
      clientName?: string;
      clientDomain?: string;
    }
  ): ScrapedPageResponseDTO {
    return {
      ...baseResponse,
      clientName: additionalData.clientName,
      clientDomain: additionalData.clientDomain
    };
  }

  // Convert array of models to DTOs
  toDTOArray(models: ScrapedPage[]): ScrapedPageDTO[] {
    return models.map(model => this.toDTO(model));
  }

  // Convert array of models to response DTOs
  toResponseArray(models: ScrapedPage[]): ScrapedPageResponseDTO[] {
    return models.map(model => this.toResponse(model));
  }

  // Convert array of models to list responses
  toListResponseArray(models: ScrapedPage[]): ScrapedPageListResponseDTO[] {
    return models.map(model => this.toListResponse(model));
  }

  // Sanitize content for different contexts
  sanitizeForSearch(model: ScrapedPage): { title: string; content: string; url: string } {
    return {
      title: model.title,
      content: this.generateContentPreview(model.content, 500),
      url: model.url
    };
  }
}

// Export singleton instance
export const scrapedPageMapper = new ScrapedPageMapper();

// Utility functions
export const scrapedPageMapperUtils = {
  // Check if page is recently scraped
  isRecentlyScraped: (page: ScrapedPage, hoursThreshold: number = 24): boolean => {
    const threshold = new Date();
    threshold.setHours(threshold.getHours() - hoursThreshold);
    return page.lastScraped > threshold;
  },

  // Get scraping status color for UI
  getStatusColor: (status: string): string => {
    const colors = {
      success: '#10B981',
      pending: '#F59E0B',
      failed: '#EF4444',
      skipped: '#6B7280'
    };
    return colors[status as keyof typeof colors] || colors.skipped;
  },

  // Format page for search results
  formatForSearch: (page: ScrapedPage): { id: string; title: string; url: string; preview: string } => {
    return {
      id: page.id,
      title: page.title,
      url: page.url,
      preview: scrapedPageMapper['generateContentPreview'](page.content, 150)
    };
  },

  // Calculate reading time estimate
  estimateReadingTime: (wordCount: number): number => {
    const wordsPerMinute = 200; // Average reading speed
    return Math.ceil(wordCount / wordsPerMinute);
  }
};
