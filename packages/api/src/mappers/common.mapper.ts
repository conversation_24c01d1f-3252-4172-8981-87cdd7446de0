/**
 * Common mapping utilities and base interfaces
 */

// Base mapper interface
export interface BaseMapper<TModel, TDTO, TResponse> {
  toDTO(model: TModel): TDTO;
  toResponse(model: TModel): TResponse;
  toModel(dto: TDTO): Partial<TModel>;
}

// Pagination response interface
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    page: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Common response metadata
export interface ResponseMetadata {
  timestamp: string;
  version: string;
  requestId?: string;
}

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: ResponseMetadata;
}

// Utility functions for common transformations
export const mapperUtils = {
  // Convert database timestamp to ISO string
  formatTimestamp: (date: Date | null | undefined): string | undefined => {
    return date ? date.toISOString() : undefined;
  },

  // Convert ISO string to Date object
  parseTimestamp: (timestamp: string | null | undefined): Date | undefined => {
    return timestamp ? new Date(timestamp) : undefined;
  },

  // Safely parse JSON string
  parseJson: <T>(jsonString: string | null | undefined, defaultValue: T): T => {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString);
    } catch {
      return defaultValue;
    }
  },

  // Safely stringify JSON object
  stringifyJson: (obj: any): string => {
    try {
      return JSON.stringify(obj);
    } catch {
      return '{}';
    }
  },

  // Remove undefined/null values from object
  cleanObject: <T extends Record<string, any>>(obj: T): Partial<T> => {
    const cleaned: Partial<T> = {};
    Object.keys(obj).forEach(key => {
      if (obj[key] !== undefined && obj[key] !== null) {
        cleaned[key as keyof T] = obj[key];
      }
    });
    return cleaned;
  },

  // Create pagination metadata
  createPaginationMeta: (
    total: number,
    limit: number,
    offset: number
  ): PaginatedResponse<any>['pagination'] => {
    const page = Math.floor(offset / limit) + 1;
    const totalPages = Math.ceil(total / limit);
    
    return {
      total,
      limit,
      offset,
      page,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1
    };
  },

  // Create API response wrapper
  createApiResponse: <T>(
    data?: T,
    error?: { code: string; message: string; details?: any },
    requestId?: string
  ): ApiResponse<T> => {
    return {
      success: !error,
      data,
      error,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0',
        requestId
      }
    };
  },

  // Create success response
  createSuccessResponse: <T>(data: T, requestId?: string): ApiResponse<T> => {
    return mapperUtils.createApiResponse(data, undefined, requestId);
  },

  // Create error response
  createErrorResponse: (
    code: string,
    message: string,
    details?: any,
    requestId?: string
  ): ApiResponse<never> => {
    return mapperUtils.createApiResponse(undefined, { code, message, details }, requestId);
  },

  // Convert snake_case to camelCase
  toCamelCase: (str: string): string => {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  },

  // Convert camelCase to snake_case
  toSnakeCase: (str: string): string => {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  },

  // Transform object keys from snake_case to camelCase
  keysToCamelCase: <T extends Record<string, any>>(obj: T): any => {
    const transformed: any = {};
    Object.keys(obj).forEach(key => {
      const camelKey = mapperUtils.toCamelCase(key);
      transformed[camelKey] = obj[key];
    });
    return transformed;
  },

  // Transform object keys from camelCase to snake_case
  keysToSnakeCase: <T extends Record<string, any>>(obj: T): any => {
    const transformed: any = {};
    Object.keys(obj).forEach(key => {
      const snakeKey = mapperUtils.toSnakeCase(key);
      transformed[snakeKey] = obj[key];
    });
    return transformed;
  },

  // Validate and transform URL
  normalizeUrl: (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.toString();
    } catch {
      return url; // Return original if invalid
    }
  },

  // Extract domain from URL
  extractDomain: (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return url; // Return original if invalid
    }
  },

  // Truncate text with ellipsis
  truncateText: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  },

  // Calculate word count
  calculateWordCount: (text: string): number => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  },

  // Generate content hash
  generateContentHash: (content: string): string => {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex');
  }
};

// Base DTO interfaces
export interface BaseDTO {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface BaseCreateDTO {
  [key: string]: any;
}

export interface BaseUpdateDTO {
  [key: string]: any;
}

// Search and filter interfaces
export interface SearchOptions {
  query?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface FilterOptions {
  [key: string]: any;
}

// Statistics interfaces
export interface BaseStats {
  total: number;
  active: number;
  inactive: number;
}

export interface TimeSeriesData {
  date: string;
  value: number;
}

export interface StatsResponse extends BaseStats {
  timeSeries?: TimeSeriesData[];
  breakdown?: Record<string, number>;
}
