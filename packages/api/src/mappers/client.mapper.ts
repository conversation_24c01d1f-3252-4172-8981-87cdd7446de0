/**
 * Client data transformation utilities
 */

import { Client } from '../models/Client';
import { Client as ClientInterface, ClientConfig, CreateClientRequest, UpdateClientRequest } from '../types';
import { BaseMapper, BaseDTO, mapperUtils } from './common.mapper';

// Client DTO interfaces
export interface ClientDTO extends BaseDTO {
  name: string;
  domain: string;
  apiKey: string;
  config: ClientConfig;
}

export interface ClientCreateDTO {
  name: string;
  domain: string;
  config?: ClientConfig;
}

export interface ClientUpdateDTO {
  name?: string;
  domain?: string;
  config?: ClientConfig;
}

export interface ClientResponseDTO extends ClientDTO {
  scrapedPagesCount?: number;
  activeScrapedPagesCount?: number;
  lastActivity?: string;
}

export interface ClientListResponseDTO {
  id: string;
  name: string;
  domain: string;
  createdAt: string;
  updatedAt: string;
  scrapedPagesCount?: number;
  activeScrapedPagesCount?: number;
}

export interface ClientStatsDTO {
  totalClients: number;
  activeClients: number;
  totalScrapedPages: number;
  totalActivePages: number;
  averagePagesPerClient: number;
  topClientsByPages: Array<{
    id: string;
    name: string;
    domain: string;
    pagesCount: number;
  }>;
}

// Client mapper class
export class ClientMapper implements BaseMapper<Client, ClientDTO, ClientResponseDTO> {
  // Convert Sequelize model to DTO
  toDTO(model: Client): ClientDTO {
    return {
      id: model.id,
      name: model.name,
      domain: model.domain,
      apiKey: model.apiKey,
      config: model.config,
      createdAt: mapperUtils.formatTimestamp(model.createdAt)!,
      updatedAt: mapperUtils.formatTimestamp(model.updatedAt)!
    };
  }

  // Convert Sequelize model to response DTO (with additional data)
  toResponse(model: Client): ClientResponseDTO {
    const baseDto = this.toDTO(model);
    return {
      ...baseDto,
      // Additional fields can be populated by the service layer
      scrapedPagesCount: undefined,
      activeScrapedPagesCount: undefined,
      lastActivity: undefined
    };
  }

  // Convert DTO to partial model data for creation/updates
  toModel(dto: ClientDTO): Partial<Client> {
    return mapperUtils.cleanObject({
      id: dto.id,
      name: dto.name,
      domain: dto.domain,
      apiKey: dto.apiKey,
      config: dto.config,
      createdAt: mapperUtils.parseTimestamp(dto.createdAt),
      updatedAt: mapperUtils.parseTimestamp(dto.updatedAt)
    });
  }

  // Convert create request to model data
  fromCreateRequest(request: CreateClientRequest): Partial<Client> {
    return mapperUtils.cleanObject({
      name: request.name,
      domain: mapperUtils.normalizeUrl(request.domain),
      config: request.config || {}
    });
  }

  // Convert update request to model data
  fromUpdateRequest(request: UpdateClientRequest): Partial<Client> {
    return mapperUtils.cleanObject({
      name: request.name,
      domain: request.domain ? mapperUtils.normalizeUrl(request.domain) : undefined,
      config: request.config
    });
  }

  // Convert model to list response (minimal data)
  toListResponse(model: Client): ClientListResponseDTO {
    return {
      id: model.id,
      name: model.name,
      domain: model.domain,
      createdAt: mapperUtils.formatTimestamp(model.createdAt)!,
      updatedAt: mapperUtils.formatTimestamp(model.updatedAt)!
    };
  }

  // Convert model to interface (for backward compatibility)
  toInterface(model: Client): ClientInterface {
    return {
      id: model.id,
      name: model.name,
      domain: model.domain,
      apiKey: model.apiKey,
      config: model.config,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt
    };
  }

  // Convert interface to model data
  fromInterface(clientInterface: ClientInterface): Partial<Client> {
    return mapperUtils.cleanObject({
      id: clientInterface.id,
      name: clientInterface.name,
      domain: clientInterface.domain,
      apiKey: clientInterface.apiKey,
      config: clientInterface.config,
      createdAt: clientInterface.createdAt,
      updatedAt: clientInterface.updatedAt
    });
  }

  // Enhance response with additional data
  enhanceResponse(
    baseResponse: ClientResponseDTO,
    additionalData: {
      scrapedPagesCount?: number;
      activeScrapedPagesCount?: number;
      lastActivity?: Date;
    }
  ): ClientResponseDTO {
    return {
      ...baseResponse,
      scrapedPagesCount: additionalData.scrapedPagesCount,
      activeScrapedPagesCount: additionalData.activeScrapedPagesCount,
      lastActivity: mapperUtils.formatTimestamp(additionalData.lastActivity)
    };
  }

  // Enhance list response with additional data
  enhanceListResponse(
    baseResponse: ClientListResponseDTO,
    additionalData: {
      scrapedPagesCount?: number;
      activeScrapedPagesCount?: number;
    }
  ): ClientListResponseDTO {
    return {
      ...baseResponse,
      scrapedPagesCount: additionalData.scrapedPagesCount,
      activeScrapedPagesCount: additionalData.activeScrapedPagesCount
    };
  }

  // Sanitize client data for public API (remove sensitive info)
  toPublicResponse(model: Client): Omit<ClientResponseDTO, 'apiKey'> {
    const response = this.toResponse(model);
    const { apiKey, ...publicResponse } = response;
    return publicResponse;
  }

  // Convert array of models to DTOs
  toDTOArray(models: Client[]): ClientDTO[] {
    return models.map(model => this.toDTO(model));
  }

  // Convert array of models to response DTOs
  toResponseArray(models: Client[]): ClientResponseDTO[] {
    return models.map(model => this.toResponse(model));
  }

  // Convert array of models to list responses
  toListResponseArray(models: Client[]): ClientListResponseDTO[] {
    return models.map(model => this.toListResponse(model));
  }

  // Validate client configuration
  validateConfig(config: ClientConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.primaryColor && !/^#[0-9A-Fa-f]{6}$/.test(config.primaryColor)) {
      errors.push('Primary color must be a valid hex color code');
    }

    if (config.askAnythingText && config.askAnythingText.length > 200) {
      errors.push('Ask anything text must be 200 characters or less');
    }

    if (config.logo && !mapperUtils.normalizeUrl(config.logo)) {
      errors.push('Logo must be a valid URL');
    }

    if (config.placeholder && config.placeholder.length > 100) {
      errors.push('Placeholder text must be 100 characters or less');
    }

    if (config.siteName && config.siteName.length > 100) {
      errors.push('Site name must be 100 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const clientMapper = new ClientMapper();

// Utility functions
export const clientMapperUtils = {
  // Extract domain from URL
  extractDomainFromUrl: (url: string): string => {
    return mapperUtils.extractDomain(url);
  },

  // Generate client display name
  generateDisplayName: (client: Client): string => {
    return `${client.name} (${mapperUtils.extractDomain(client.domain)})`;
  },

  // Check if client is recently active
  isRecentlyActive: (client: Client, daysThreshold: number = 7): boolean => {
    const threshold = new Date();
    threshold.setDate(threshold.getDate() - daysThreshold);
    return client.updatedAt > threshold;
  },

  // Format client for search results
  formatForSearch: (client: Client): { id: string; name: string; domain: string; displayName: string } => {
    return {
      id: client.id,
      name: client.name,
      domain: client.domain,
      displayName: clientMapperUtils.generateDisplayName(client)
    };
  }
};
