/**
 * Database seeding script for development and testing.
 * This script populates the database with comprehensive sample data including demo clients,
 * scraped pages with various statuses, suggestions, analytics, and test URLs for scraping functionality.
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import db from './connection';

// Helper function to generate content hash
function generateContentHash(content: string): string {
  return crypto.createHash('sha256').update(content).digest('hex');
}

// Helper function to calculate word count
function calculateWordCount(content: string): number {
  return content.trim().split(/\s+/).filter(word => word.length > 0).length;
}

// Create comprehensive sample pages for testing
async function createSamplePages(client: any) {
  const basePages = [
    {
      url: `https://${client.domain}/about`,
      title: 'About Us - Company Overview',
      content: `Welcome to ${client.name}! We are a leading technology company dedicated to innovation and excellence. Our mission is to provide cutting-edge solutions that transform businesses and improve lives. Founded in 2020, we have grown from a small startup to a trusted partner for companies worldwide. Our team of experts brings decades of experience in software development, artificial intelligence, and digital transformation. We believe in the power of technology to solve complex problems and create meaningful impact. Our core values include integrity, innovation, collaboration, and customer success. We are committed to sustainability and responsible business practices. Join us on our journey to shape the future of technology.`,
      description: 'Learn about our company history, mission, values, and team',
      thumbnail: 'https://via.placeholder.com/400x200/667eea/ffffff?text=About+Us',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-15T10:30:00Z',
        author: 'Marketing Team',
        category: 'company-info',
        tags: ['about', 'company', 'mission', 'values']
      }
    },
    {
      url: `https://${client.domain}/products`,
      title: 'Our Products & Services',
      content: `Discover our comprehensive suite of products and services designed to meet your business needs. Our flagship product, AI Assistant Pro, leverages advanced machine learning algorithms to provide intelligent automation and decision support. We also offer Cloud Infrastructure Solutions that provide scalable, secure, and reliable hosting for your applications. Our Data Analytics Platform helps you unlock insights from your data with powerful visualization and reporting tools. For developers, we provide APIs and SDKs that make integration seamless and efficient. Our Professional Services team offers consulting, implementation, and support to ensure your success. All our products are built with security, scalability, and user experience in mind. We continuously innovate and update our offerings based on customer feedback and market trends.`,
      description: 'Explore our range of innovative products and services',
      thumbnail: 'https://via.placeholder.com/400x200/10b981/ffffff?text=Products',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-20T14:15:00Z',
        author: 'Product Team',
        category: 'products',
        tags: ['products', 'services', 'AI', 'cloud', 'analytics']
      }
    },
    {
      url: `https://${client.domain}/pricing`,
      title: 'Pricing Plans - Choose Your Plan',
      content: `We offer flexible pricing plans to suit businesses of all sizes. Our Starter Plan at $29/month includes basic features perfect for small teams and startups. The Professional Plan at $99/month adds advanced analytics, priority support, and increased usage limits. For enterprises, our Enterprise Plan offers custom pricing with unlimited usage, dedicated support, and advanced security features. All plans include a 14-day free trial, no setup fees, and the ability to upgrade or downgrade at any time. We also offer volume discounts for annual subscriptions and non-profit organizations. Our transparent pricing means no hidden fees or surprise charges. Contact our sales team for custom enterprise solutions and bulk pricing options.`,
      description: 'View our pricing plans and choose the right option for your needs',
      thumbnail: 'https://via.placeholder.com/400x200/f59e0b/ffffff?text=Pricing',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-18T09:45:00Z',
        author: 'Sales Team',
        category: 'pricing',
        tags: ['pricing', 'plans', 'subscription', 'enterprise']
      }
    },
    {
      url: `https://${client.domain}/contact`,
      title: 'Contact Us - Get in Touch',
      content: `We'd love to hear from you! Reach out to our team for any questions, support, or partnership opportunities. Our main office is located at 123 Tech Street, San Francisco, CA 94105. You can call us at (555) 123-4567 or email us at hello@${client.domain}. For technical support, please use support@${client.domain} or visit our help center. Our customer success team is available Monday through Friday, 9 AM to 6 PM PST. For sales inquiries, contact sales@${client.domain} or schedule a demo through our website. Follow us on social media for the latest updates and news. We're committed to responding to all inquiries within 24 hours.`,
      description: 'Contact information and ways to reach our team',
      thumbnail: 'https://via.placeholder.com/400x200/8b5cf6/ffffff?text=Contact',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-10T16:20:00Z',
        author: 'Support Team',
        category: 'contact',
        tags: ['contact', 'support', 'office', 'phone', 'email']
      }
    }
  ];

  // Add some pages with different statuses for testing
  const testPages = [
    {
      url: `https://${client.domain}/blog/getting-started`,
      title: 'Getting Started Guide',
      content: `This comprehensive guide will help you get started with our platform in just a few minutes. First, create your account and verify your email address. Next, complete your profile and set up your organization. Then, invite team members and configure permissions. Finally, start using our tools and explore advanced features. Our onboarding process is designed to be simple and intuitive.`,
      description: 'Step-by-step guide to get started with our platform',
      thumbnail: 'https://via.placeholder.com/400x200/06b6d4/ffffff?text=Guide',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-22T11:30:00Z',
        author: 'Documentation Team',
        category: 'documentation',
        tags: ['guide', 'getting-started', 'onboarding', 'tutorial']
      }
    },
    {
      url: `https://${client.domain}/api/docs`,
      title: 'API Documentation',
      content: `Our REST API provides programmatic access to all platform features. Authentication is handled via API keys that you can generate in your dashboard. All endpoints return JSON responses and support standard HTTP methods. Rate limiting is applied to ensure fair usage. We provide SDKs for popular programming languages including Python, JavaScript, Java, and Go. Comprehensive examples and code samples are available for each endpoint.`,
      description: 'Complete API documentation and reference',
      thumbnail: null,
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-25T13:45:00Z',
        author: 'Engineering Team',
        category: 'documentation',
        tags: ['API', 'documentation', 'REST', 'SDK', 'integration']
      }
    },
    {
      url: `https://${client.domain}/careers`,
      title: 'Careers - Join Our Team',
      content: `We're always looking for talented individuals to join our growing team. We offer competitive salaries, comprehensive benefits, flexible work arrangements, and opportunities for professional growth. Current openings include Software Engineers, Product Managers, Data Scientists, and Customer Success Specialists. We value diversity, inclusion, and work-life balance. Our culture emphasizes collaboration, innovation, and continuous learning.`,
      description: 'Career opportunities and job openings',
      thumbnail: 'https://via.placeholder.com/400x200/ec4899/ffffff?text=Careers',
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-12T08:15:00Z',
        author: 'HR Team',
        category: 'careers',
        tags: ['careers', 'jobs', 'hiring', 'team', 'culture']
      }
    },
    {
      url: `https://${client.domain}/privacy`,
      title: 'Privacy Policy',
      content: `Your privacy is important to us. This policy explains how we collect, use, and protect your personal information. We only collect data necessary to provide our services and improve user experience. We never sell your personal information to third parties. All data is encrypted in transit and at rest. You have the right to access, modify, or delete your personal data at any time.`,
      description: 'Our privacy policy and data protection practices',
      thumbnail: null,
      scrapingStatus: 'success',
      errorMessage: null,
      language: 'en',
      metadata: {
        lastModified: '2024-01-05T14:30:00Z',
        author: 'Legal Team',
        category: 'legal',
        tags: ['privacy', 'policy', 'data-protection', 'GDPR']
      }
    },
    {
      url: `https://${client.domain}/maintenance`,
      title: 'Maintenance Page',
      content: '',
      description: 'Site under maintenance',
      thumbnail: null,
      scrapingStatus: 'failed',
      errorMessage: 'HTTP 503: Service temporarily unavailable',
      language: null,
      metadata: {
        error: 'maintenance_mode',
        retryAfter: '2024-01-30T02:00:00Z'
      }
    },
    {
      url: `https://${client.domain}/404-page`,
      title: 'Page Not Found',
      content: '',
      description: 'Page not found',
      thumbnail: null,
      scrapingStatus: 'failed',
      errorMessage: 'HTTP 404: Page not found',
      language: null,
      metadata: {
        error: 'not_found',
        statusCode: 404
      }
    },
    {
      url: `https://${client.domain}/pending-review`,
      title: 'Content Under Review',
      content: '',
      description: 'Content pending review',
      thumbnail: null,
      scrapingStatus: 'pending',
      errorMessage: null,
      language: null,
      metadata: {
        status: 'pending_review',
        queuedAt: '2024-01-28T10:00:00Z'
      }
    }
  ];

  return [...basePages, ...testPages];
}

// Create sample analytics data
async function createSampleAnalytics(clientId: string) {
  const analyticsData = [
    {
      type: 'question',
      data: {
        question: 'What are your pricing plans?',
        response: 'Generated AI response about pricing',
        responseTime: 1200,
        satisfied: true
      }
    },
    {
      type: 'suggestion',
      data: {
        suggestion: 'How can I contact support?',
        clicked: true,
        position: 1
      }
    },
    {
      type: 'link',
      data: {
        url: `https://example.com/contact`,
        title: 'Contact Us',
        clicked: true,
        position: 2
      }
    },
    {
      type: 'question',
      data: {
        question: 'Do you offer free trials?',
        response: 'Yes, we offer 14-day free trials',
        responseTime: 800,
        satisfied: true
      }
    },
    {
      type: 'question',
      data: {
        question: 'How do I get started?',
        response: 'Getting started is easy...',
        responseTime: 950,
        satisfied: false
      }
    }
  ];

  for (const analytics of analyticsData) {
    await db.query(`
      INSERT INTO analytics (id, client_id, type, data, timestamp, user_agent, ip_address)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      uuidv4(),
      clientId,
      analytics.type,
      JSON.stringify(analytics.data),
      new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random time in last 7 days
      'Mozilla/5.0 (compatible; TestBot/1.0)',
      '192.168.1.' + Math.floor(Math.random() * 255)
    ]);
  }
}

// Create test URLs for scraping functionality
async function createTestScrapingUrls() {
  console.log('📝 Test URLs for scraping (use these in your tests):');
  console.log('');

  const testUrls = [
    {
      category: '✅ Working URLs (should scrape successfully)',
      urls: [
        'https://example.com',
        'https://httpbin.org/html',
        'https://jsonplaceholder.typicode.com',
        'https://github.com/microsoft/TypeScript',
        'https://nodejs.org/en/about',
        'https://developer.mozilla.org/en-US/docs/Web/JavaScript'
      ]
    },
    {
      category: '⚠️  Challenging URLs (may require JavaScript rendering)',
      urls: [
        'https://react.dev',
        'https://vuejs.org',
        'https://angular.io'
      ]
    },
    {
      category: '❌ URLs that should fail (for error testing)',
      urls: [
        'https://httpbin.org/status/404',
        'https://httpbin.org/status/500',
        'https://httpbin.org/delay/30',
        'https://invalid-domain-that-does-not-exist.com',
        'https://httpbin.org/redirect/10'
      ]
    },
    {
      category: '🔒 URLs requiring special handling',
      urls: [
        'https://httpbin.org/robots.txt',
        'https://httpbin.org/redirect/3',
        'https://httpbin.org/gzip'
      ]
    }
  ];

  for (const category of testUrls) {
    console.log(`\n${category.category}:`);
    for (const url of category.urls) {
      console.log(`  • ${url}`);
    }
  }

  console.log('\n💡 Usage examples:');
  console.log('  # Single URL scraping:');
  console.log('  POST /api/clients/{clientId}/scrape');
  console.log('  { "url": "https://example.com" }');
  console.log('');
  console.log('  # Bulk scraping:');
  console.log('  POST /api/clients/{clientId}/scrape/bulk');
  console.log('  { "urls": ["https://example.com", "https://httpbin.org/html"] }');
  console.log('');
}

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Test database connection
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // Create demo clients
    console.log('👥 Creating demo clients...');

    const demoClients = [
      {
        id: uuidv4(),
        name: 'Acme Corporation',
        domain: 'acme.com',
        apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
        config: {
          primaryColor: '#667eea',
          askAnythingText: 'How can we help you?',
          logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=A',
          placeholder: 'Ask us anything about our products...',
          siteName: 'Acme Corporation'
        }
      },
      {
        id: uuidv4(),
        name: 'TechStart Inc',
        domain: 'techstart.io',
        apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
        config: {
          primaryColor: '#10b981',
          askAnythingText: 'Need help?',
          logo: 'https://via.placeholder.com/40x40/10b981/ffffff?text=T',
          placeholder: 'What would you like to know?',
          siteName: 'TechStart'
        }
      },
      {
        id: uuidv4(),
        name: 'OpenSource Docs',
        domain: 'docs.example.org',
        apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
        config: {
          primaryColor: '#f59e0b',
          askAnythingText: 'Documentation help?',
          logo: 'https://via.placeholder.com/40x40/f59e0b/ffffff?text=D',
          placeholder: 'Search our documentation...',
          siteName: 'OpenSource Docs'
        }
      }
    ];

    for (const client of demoClients) {
      await db.query(
        'INSERT INTO clients (id, name, domain, api_key, config) VALUES ($1, $2, $3, $4, $5)',
        [client.id, client.name, client.domain, client.apiKey, JSON.stringify(client.config)]
      );

      // Add comprehensive sample scraped pages with various statuses
      const samplePages = await createSamplePages(client);

      for (const page of samplePages) {
        const contentHash = generateContentHash(page.content);
        const wordCount = calculateWordCount(page.content);

        await db.query(`
          INSERT INTO scraped_pages (
            id, client_id, url, title, content, description, thumbnail,
            scraping_status, error_message, content_hash, word_count,
            language, metadata, last_scraped
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        `, [
          uuidv4(),
          client.id,
          page.url,
          page.title,
          page.content,
          page.description,
          page.thumbnail,
          page.scrapingStatus,
          page.errorMessage,
          contentHash,
          wordCount,
          page.language,
          JSON.stringify(page.metadata),
          new Date()
        ]);
      }

      // Add sample suggestions
      const sampleSuggestions = [
        { question: 'What services do you offer?', category: 'general', priority: 10 },
        { question: 'How can I contact support?', category: 'support', priority: 9 },
        { question: 'What are your pricing plans?', category: 'pricing', priority: 8 },
        { question: 'How do I get started?', category: 'onboarding', priority: 7 },
        { question: 'Do you offer free trials?', category: 'pricing', priority: 6 }
      ];

      for (const suggestion of sampleSuggestions) {
        await db.query(
          'INSERT INTO bot_suggestions (id, client_id, question, category, priority) VALUES ($1, $2, $3, $4, $5)',
          [uuidv4(), client.id, suggestion.question, suggestion.category, suggestion.priority]
        );
      }

      // Add sample analytics data
      await createSampleAnalytics(client.id);

      console.log(`✅ Created demo client: ${client.name}`);
    }

    // Add test URLs for scraping functionality
    console.log('🔗 Adding test URLs for scraping...');
    await createTestScrapingUrls();

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Demo client details:');
    
    for (const client of demoClients) {
      console.log(`\n${client.name}:`);
      console.log(`  Domain: ${client.domain}`);
      console.log(`  API Key: ${client.apiKey}`);
      console.log(`  Client ID: ${client.id}`);
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

export { seedDatabase };
