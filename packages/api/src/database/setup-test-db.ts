/**
 * Complete database setup script for testing.
 * This script runs migrations and seeds the database with comprehensive test data
 * including scraping test cases, various page statuses, and analytics data.
 */

import { promises as fs } from 'fs';
import path from 'path';
import db from './connection';

async function runMigrations() {
  console.log('🔄 Running database migrations...');
  
  try {
    // Check if the new columns exist
    const result = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'scraped_pages' 
      AND column_name IN ('scraping_status', 'content_hash', 'word_count', 'language', 'metadata')
    `);
    
    if (result.rows.length < 5) {
      console.log('📝 Applying scraped_pages enhancements...');
      
      // Read and execute the migration
      const migrationPath = path.join(__dirname, 'migrations', '001_enhance_scraped_pages.sql');
      const migrationSql = await fs.readFile(migrationPath, 'utf8');
      
      // Split by semicolon and execute each statement
      const statements = migrationSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        try {
          await db.query(statement);
        } catch (error) {
          // Ignore errors for statements that might already exist
          if (!error.message.includes('already exists') && 
              !error.message.includes('duplicate key') &&
              !error.message.includes('column already exists')) {
            console.warn(`Warning in migration: ${error.message}`);
          }
        }
      }
      
      console.log('✅ Migration completed successfully');
    } else {
      console.log('✅ Database schema is up to date');
    }
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function clearExistingData() {
  console.log('🧹 Clearing existing test data...');
  
  try {
    // Clear in correct order due to foreign key constraints
    await db.query('DELETE FROM analytics');
    await db.query('DELETE FROM bot_responses');
    await db.query('DELETE FROM bot_suggestions');
    await db.query('DELETE FROM scraped_pages');
    await db.query('DELETE FROM clients');
    
    console.log('✅ Existing data cleared');
  } catch (error) {
    console.error('❌ Failed to clear data:', error);
    throw error;
  }
}

async function setupTestDatabase() {
  console.log('🚀 Setting up test database...');
  console.log('=====================================');
  
  try {
    // Test database connection
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }
    console.log('✅ Database connection established');
    
    // Run migrations
    await runMigrations();
    
    // Clear existing data
    await clearExistingData();
    
    // Seed with test data (inline to avoid import issues)
    await seedDatabaseInline();
    
    console.log('');
    console.log('🎉 Test database setup completed successfully!');
    console.log('=====================================');
    console.log('');
    console.log('🔧 What was created:');
    console.log('  • 3 demo clients with different configurations');
    console.log('  • 33+ scraped pages with various statuses (success/failed/pending)');
    console.log('  • Bot suggestions for each client');
    console.log('  • Sample analytics data');
    console.log('  • Test URLs for scraping functionality');
    console.log('');
    console.log('🧪 Ready for testing:');
    console.log('  • Scraping API endpoints');
    console.log('  • Content search and filtering');
    console.log('  • Error handling scenarios');
    console.log('  • Analytics tracking');
    console.log('  • Bot response generation');
    console.log('');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupTestDatabase();
}

// Inline seeding function to avoid import issues
async function seedDatabaseInline() {
  console.log('🌱 Starting database seeding...');

  const { v4: uuidv4 } = await import('uuid');
  const crypto = await import('crypto');

  // Helper functions
  function generateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  function calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  // Create demo clients
  console.log('👥 Creating demo clients...');

  const demoClients = [
    {
      id: uuidv4(),
      name: 'Acme Corporation',
      domain: 'acme.com',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#667eea',
        askAnythingText: 'How can we help you?',
        logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=A',
        placeholder: 'Ask us anything about our products...',
        siteName: 'Acme Corporation'
      }
    },
    {
      id: uuidv4(),
      name: 'TechStart Inc',
      domain: 'techstart.io',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#10b981',
        askAnythingText: 'Need help?',
        logo: 'https://via.placeholder.com/40x40/10b981/ffffff?text=T',
        placeholder: 'What would you like to know?',
        siteName: 'TechStart'
      }
    },
    {
      id: uuidv4(),
      name: 'OpenSource Docs',
      domain: 'docs.example.org',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#f59e0b',
        askAnythingText: 'Documentation help?',
        logo: 'https://via.placeholder.com/40x40/f59e0b/ffffff?text=D',
        placeholder: 'Search our documentation...',
        siteName: 'OpenSource Docs'
      }
    }
  ];

  for (const client of demoClients) {
    await db.query(
      'INSERT INTO clients (id, name, domain, api_key, config) VALUES ($1, $2, $3, $4, $5)',
      [client.id, client.name, client.domain, client.apiKey, JSON.stringify(client.config)]
    );

    // Add sample scraped pages
    const samplePages = [
      {
        url: `https://${client.domain}/about`,
        title: 'About Us - Company Overview',
        content: `Welcome to ${client.name}! We are a leading technology company dedicated to innovation and excellence. Our mission is to provide cutting-edge solutions that transform businesses and improve lives.`,
        description: 'Learn about our company history, mission, values, and team',
        thumbnail: 'https://via.placeholder.com/400x200/667eea/ffffff?text=About+Us',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'company-info', tags: ['about', 'company', 'mission'] }
      },
      {
        url: `https://${client.domain}/products`,
        title: 'Our Products & Services',
        content: `Discover our comprehensive suite of products and services designed to meet your business needs. Our flagship product, AI Assistant Pro, leverages advanced machine learning algorithms.`,
        description: 'Explore our range of innovative products and services',
        thumbnail: 'https://via.placeholder.com/400x200/10b981/ffffff?text=Products',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'products', tags: ['products', 'services', 'AI'] }
      },
      {
        url: `https://${client.domain}/pricing`,
        title: 'Pricing Plans - Choose Your Plan',
        content: `We offer flexible pricing plans to suit businesses of all sizes. Our Starter Plan at $29/month includes basic features perfect for small teams.`,
        description: 'View our pricing plans and choose the right option',
        thumbnail: 'https://via.placeholder.com/400x200/f59e0b/ffffff?text=Pricing',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'pricing', tags: ['pricing', 'plans', 'subscription'] }
      },
      {
        url: `https://${client.domain}/contact`,
        title: 'Contact Us - Get in Touch',
        content: `We'd love to hear from you! Reach out to our team for any questions, support, or partnership opportunities.`,
        description: 'Contact information and ways to reach our team',
        thumbnail: 'https://via.placeholder.com/400x200/8b5cf6/ffffff?text=Contact',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'contact', tags: ['contact', 'support'] }
      },
      {
        url: `https://${client.domain}/404-page`,
        title: 'Page Not Found',
        content: '',
        description: 'Page not found',
        thumbnail: null,
        scrapingStatus: 'failed',
        errorMessage: 'HTTP 404: Page not found',
        language: null,
        metadata: { error: 'not_found', statusCode: 404 }
      },
      {
        url: `https://${client.domain}/pending-review`,
        title: 'Content Under Review',
        content: '',
        description: 'Content pending review',
        thumbnail: null,
        scrapingStatus: 'pending',
        errorMessage: null,
        language: null,
        metadata: { status: 'pending_review' }
      }
    ];

    for (const page of samplePages) {
      const contentHash = generateContentHash(page.content);
      const wordCount = calculateWordCount(page.content);

      await db.query(`
        INSERT INTO scraped_pages (
          id, client_id, url, title, content, description, thumbnail,
          scraping_status, error_message, content_hash, word_count,
          language, metadata, last_scraped
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      `, [
        uuidv4(),
        client.id,
        page.url,
        page.title,
        page.content,
        page.description,
        page.thumbnail,
        page.scrapingStatus,
        page.errorMessage,
        contentHash,
        wordCount,
        page.language,
        JSON.stringify(page.metadata),
        new Date()
      ]);
    }

    // Add sample suggestions
    const sampleSuggestions = [
      { question: 'What services do you offer?', category: 'general', priority: 10 },
      { question: 'How can I contact support?', category: 'support', priority: 9 },
      { question: 'What are your pricing plans?', category: 'pricing', priority: 8 },
      { question: 'How do I get started?', category: 'onboarding', priority: 7 },
      { question: 'Do you offer free trials?', category: 'pricing', priority: 6 }
    ];

    for (const suggestion of sampleSuggestions) {
      await db.query(
        'INSERT INTO bot_suggestions (id, client_id, question, category, priority) VALUES ($1, $2, $3, $4, $5)',
        [uuidv4(), client.id, suggestion.question, suggestion.category, suggestion.priority]
      );
    }

    console.log(`✅ Created demo client: ${client.name} (ID: ${client.id})`);
    console.log(`   API Key: ${client.apiKey}`);
  }

  // Add test URLs info
  console.log('');
  console.log('🔗 Test URLs for scraping:');
  console.log('  ✅ Working: https://example.com, https://httpbin.org/html');
  console.log('  ❌ Failing: https://httpbin.org/status/404, https://httpbin.org/status/500');
  console.log('  ⚠️  Challenging: https://react.dev (requires JS)');

  console.log('🎉 Database seeding completed successfully!');
}

export { setupTestDatabase, runMigrations };
