-- AI Answer Bot Database Schema
-- This file contains the complete PostgreSQL database schema for the AI Answer Bot system,
-- including tables for clients, scraped pages, suggestions, analytics, and related indexes.

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Clients table - stores client configuration and API keys
CREATE TABLE IF NOT EXISTS clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL UNIQUE,
    api_key VARCHAR(255) NOT NULL UNIQUE,
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scraped pages table - stores website content for each client
CREATE TABLE IF NOT EXISTS scraped_pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title VARCHAR(500),
    content TEXT,
    description TEXT,
    thumbnail VARCHAR(500),
    last_scraped TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scraping_status VARCHAR(20) DEFAULT 'success' CHECK (scraping_status IN ('pending', 'success', 'failed', 'skipped')),
    error_message TEXT,
    content_hash VARCHAR(64),
    word_count INTEGER DEFAULT 0,
    language VARCHAR(10),
    metadata JSONB DEFAULT '{}',
    UNIQUE(client_id, url)
);

-- Bot suggestions table - stores predefined questions for each client
CREATE TABLE IF NOT EXISTS bot_suggestions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    category VARCHAR(100),
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics table - tracks user interactions
CREATE TABLE IF NOT EXISTS analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bot responses table - stores generated responses for caching
CREATE TABLE IF NOT EXISTS bot_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    question_hash VARCHAR(64) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    suggestions JSONB DEFAULT '[]',
    link_previews JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    UNIQUE(client_id, question_hash)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_domain ON clients(domain);
CREATE INDEX IF NOT EXISTS idx_clients_api_key ON clients(api_key);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_client_id ON scraped_pages(client_id);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_url ON scraped_pages(url);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_active ON scraped_pages(client_id, is_active);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_status ON scraped_pages(client_id, scraping_status);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_language ON scraped_pages(language);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_content_hash ON scraped_pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_last_scraped ON scraped_pages(last_scraped);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_word_count ON scraped_pages(word_count);
-- Full-text search index for content
CREATE INDEX IF NOT EXISTS idx_scraped_pages_content_search ON scraped_pages USING gin(to_tsvector('english', title || ' ' || COALESCE(content, '') || ' ' || COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_bot_suggestions_client_id ON bot_suggestions(client_id);
CREATE INDEX IF NOT EXISTS idx_bot_suggestions_active ON bot_suggestions(client_id, is_active);
CREATE INDEX IF NOT EXISTS idx_bot_suggestions_priority ON bot_suggestions(client_id, priority DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_client_id ON analytics(client_id);
CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_type ON analytics(client_id, type);
CREATE INDEX IF NOT EXISTS idx_bot_responses_client_id ON bot_responses(client_id);
CREATE INDEX IF NOT EXISTS idx_bot_responses_hash ON bot_responses(client_id, question_hash);
CREATE INDEX IF NOT EXISTS idx_bot_responses_expires ON bot_responses(expires_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scraped_pages_updated_at BEFORE UPDATE ON scraped_pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bot_suggestions_updated_at BEFORE UPDATE ON bot_suggestions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
