-- Migration: Enhance scraped_pages table with additional tracking fields
-- This migration adds new columns for better scraping tracking, content versioning, and metadata storage

-- Add new columns to scraped_pages table
ALTER TABLE scraped_pages 
ADD COLUMN IF NOT EXISTS scraping_status VARCHAR(20) DEFAULT 'success' CHECK (scraping_status IN ('pending', 'success', 'failed', 'skipped')),
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64),
ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS language VARCHAR(10),
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Update existing records to have proper word counts and content hashes
UPDATE scraped_pages 
SET word_count = array_length(string_to_array(trim(content), ' '), 1)
WHERE word_count IS NULL OR word_count = 0;

-- Create new indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scraped_pages_status ON scraped_pages(client_id, scraping_status);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_language ON scraped_pages(language);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_content_hash ON scraped_pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_last_scraped ON scraped_pages(last_scraped);
CREATE INDEX IF NOT EXISTS idx_scraped_pages_word_count ON scraped_pages(word_count);

-- Full-text search index for content (PostgreSQL specific)
CREATE INDEX IF NOT EXISTS idx_scraped_pages_content_search 
ON scraped_pages USING gin(to_tsvector('english', title || ' ' || COALESCE(content, '') || ' ' || COALESCE(description, '')));

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_scraped_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_scraped_pages_updated_at
    BEFORE UPDATE ON scraped_pages
    FOR EACH ROW
    EXECUTE FUNCTION update_scraped_pages_updated_at();
