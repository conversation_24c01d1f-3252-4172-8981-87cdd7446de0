/**
 * Simple database setup script using raw SQL.
 * This script avoids Sequelize model loading issues and uses direct SQL queries.
 */

import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// Create a simple PostgreSQL connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'ai_answer_bot',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

// Helper functions
function generateContentHash(content: string): string {
  return crypto.createHash('sha256').update(content).digest('hex');
}

function calculateWordCount(content: string): number {
  return content.trim().split(/\s+/).filter(word => word.length > 0).length;
}

async function runMigrations() {
  console.log('🔄 Running database migrations...');
  
  try {
    // Check if the new columns exist
    const result = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'scraped_pages' 
      AND column_name IN ('scraping_status', 'content_hash', 'word_count', 'language', 'metadata')
    `);
    
    if (result.rows.length < 5) {
      console.log('📝 Adding new columns to scraped_pages...');
      
      // Add new columns
      const alterQueries = [
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS scraping_status VARCHAR(20) DEFAULT 'success'`,
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS error_message TEXT`,
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64)`,
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0`,
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS language VARCHAR(10)`,
        `ALTER TABLE scraped_pages ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'`,
        `ALTER TABLE scraped_pages ADD CONSTRAINT IF NOT EXISTS scraped_pages_status_check CHECK (scraping_status IN ('pending', 'success', 'failed', 'skipped'))`
      ];
      
      for (const query of alterQueries) {
        try {
          await pool.query(query);
        } catch (error) {
          // Ignore errors for columns that might already exist
          if (!error.message.includes('already exists')) {
            console.warn(`Warning: ${error.message}`);
          }
        }
      }
      
      // Add indexes
      const indexQueries = [
        `CREATE INDEX IF NOT EXISTS idx_scraped_pages_status ON scraped_pages(client_id, scraping_status)`,
        `CREATE INDEX IF NOT EXISTS idx_scraped_pages_language ON scraped_pages(language)`,
        `CREATE INDEX IF NOT EXISTS idx_scraped_pages_content_hash ON scraped_pages(content_hash)`,
        `CREATE INDEX IF NOT EXISTS idx_scraped_pages_word_count ON scraped_pages(word_count)`
      ];
      
      for (const query of indexQueries) {
        try {
          await pool.query(query);
        } catch (error) {
          if (!error.message.includes('already exists')) {
            console.warn(`Warning: ${error.message}`);
          }
        }
      }
      
      console.log('✅ Migration completed successfully');
    } else {
      console.log('✅ Database schema is up to date');
    }
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function clearExistingData() {
  console.log('🧹 Clearing existing test data...');
  
  try {
    // Clear in correct order due to foreign key constraints
    await pool.query('DELETE FROM analytics');
    await pool.query('DELETE FROM bot_responses');
    await pool.query('DELETE FROM bot_suggestions');
    await pool.query('DELETE FROM scraped_pages');
    await pool.query('DELETE FROM clients');
    
    console.log('✅ Existing data cleared');
  } catch (error) {
    console.error('❌ Failed to clear data:', error);
    throw error;
  }
}

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  // Create demo clients
  console.log('👥 Creating demo clients...');
  
  const demoClients = [
    {
      id: uuidv4(),
      name: 'Acme Corporation',
      domain: 'acme.com',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#667eea',
        askAnythingText: 'How can we help you?',
        logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=A',
        placeholder: 'Ask us anything about our products...',
        siteName: 'Acme Corporation'
      }
    },
    {
      id: uuidv4(),
      name: 'TechStart Inc',
      domain: 'techstart.io',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#10b981',
        askAnythingText: 'Need help?',
        logo: 'https://via.placeholder.com/40x40/10b981/ffffff?text=T',
        placeholder: 'What would you like to know?',
        siteName: 'TechStart'
      }
    },
    {
      id: uuidv4(),
      name: 'OpenSource Docs',
      domain: 'docs.example.org',
      apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
      config: {
        primaryColor: '#f59e0b',
        askAnythingText: 'Documentation help?',
        logo: 'https://via.placeholder.com/40x40/f59e0b/ffffff?text=D',
        placeholder: 'Search our documentation...',
        siteName: 'OpenSource Docs'
      }
    }
  ];

  for (const client of demoClients) {
    await pool.query(
      'INSERT INTO clients (id, name, domain, api_key, config) VALUES ($1, $2, $3, $4, $5)',
      [client.id, client.name, client.domain, client.apiKey, JSON.stringify(client.config)]
    );

    // Add sample scraped pages
    const samplePages = [
      {
        url: `https://${client.domain}/about`,
        title: 'About Us - Company Overview',
        content: `Welcome to ${client.name}! We are a leading technology company dedicated to innovation and excellence. Our mission is to provide cutting-edge solutions that transform businesses and improve lives. Founded in 2020, we have grown from a small startup to a trusted partner for companies worldwide.`,
        description: 'Learn about our company history, mission, values, and team',
        thumbnail: 'https://via.placeholder.com/400x200/667eea/ffffff?text=About+Us',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'company-info', tags: ['about', 'company', 'mission'] }
      },
      {
        url: `https://${client.domain}/products`,
        title: 'Our Products & Services',
        content: `Discover our comprehensive suite of products and services designed to meet your business needs. Our flagship product, AI Assistant Pro, leverages advanced machine learning algorithms to provide intelligent automation and decision support.`,
        description: 'Explore our range of innovative products and services',
        thumbnail: 'https://via.placeholder.com/400x200/10b981/ffffff?text=Products',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'products', tags: ['products', 'services', 'AI'] }
      },
      {
        url: `https://${client.domain}/pricing`,
        title: 'Pricing Plans - Choose Your Plan',
        content: `We offer flexible pricing plans to suit businesses of all sizes. Our Starter Plan at $29/month includes basic features perfect for small teams and startups. The Professional Plan at $99/month adds advanced analytics and priority support.`,
        description: 'View our pricing plans and choose the right option',
        thumbnail: 'https://via.placeholder.com/400x200/f59e0b/ffffff?text=Pricing',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'pricing', tags: ['pricing', 'plans', 'subscription'] }
      },
      {
        url: `https://${client.domain}/contact`,
        title: 'Contact Us - Get in Touch',
        content: `We'd love to hear from you! Reach out to our team for any questions, support, or partnership opportunities. Our main office is located at 123 Tech Street, San Francisco, CA 94105.`,
        description: 'Contact information and ways to reach our team',
        thumbnail: 'https://via.placeholder.com/400x200/8b5cf6/ffffff?text=Contact',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'contact', tags: ['contact', 'support'] }
      },
      {
        url: `https://${client.domain}/blog/getting-started`,
        title: 'Getting Started Guide',
        content: `This comprehensive guide will help you get started with our platform in just a few minutes. First, create your account and verify your email address. Next, complete your profile and set up your organization.`,
        description: 'Step-by-step guide to get started',
        thumbnail: 'https://via.placeholder.com/400x200/06b6d4/ffffff?text=Guide',
        scrapingStatus: 'success',
        errorMessage: null,
        language: 'en',
        metadata: { category: 'documentation', tags: ['guide', 'getting-started'] }
      },
      {
        url: `https://${client.domain}/404-page`,
        title: 'Page Not Found',
        content: '',
        description: 'Page not found',
        thumbnail: null,
        scrapingStatus: 'failed',
        errorMessage: 'HTTP 404: Page not found',
        language: null,
        metadata: { error: 'not_found', statusCode: 404 }
      },
      {
        url: `https://${client.domain}/pending-review`,
        title: 'Content Under Review',
        content: '',
        description: 'Content pending review',
        thumbnail: null,
        scrapingStatus: 'pending',
        errorMessage: null,
        language: null,
        metadata: { status: 'pending_review' }
      }
    ];

    for (const page of samplePages) {
      const contentHash = generateContentHash(page.content);
      const wordCount = calculateWordCount(page.content);
      
      await pool.query(`
        INSERT INTO scraped_pages (
          id, client_id, url, title, content, description, thumbnail,
          scraping_status, error_message, content_hash, word_count, 
          language, metadata, last_scraped
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      `, [
        uuidv4(), 
        client.id, 
        page.url, 
        page.title, 
        page.content, 
        page.description,
        page.thumbnail,
        page.scrapingStatus,
        page.errorMessage,
        contentHash,
        wordCount,
        page.language,
        JSON.stringify(page.metadata),
        new Date()
      ]);
    }

    // Add sample suggestions
    const sampleSuggestions = [
      { question: 'What services do you offer?', category: 'general', priority: 10 },
      { question: 'How can I contact support?', category: 'support', priority: 9 },
      { question: 'What are your pricing plans?', category: 'pricing', priority: 8 },
      { question: 'How do I get started?', category: 'onboarding', priority: 7 },
      { question: 'Do you offer free trials?', category: 'pricing', priority: 6 }
    ];

    for (const suggestion of sampleSuggestions) {
      await pool.query(
        'INSERT INTO bot_suggestions (id, client_id, question, category, priority) VALUES ($1, $2, $3, $4, $5)',
        [uuidv4(), client.id, suggestion.question, suggestion.category, suggestion.priority]
      );
    }

    console.log(`✅ Created demo client: ${client.name}`);
    console.log(`   Domain: ${client.domain}`);
    console.log(`   API Key: ${client.apiKey}`);
    console.log(`   Client ID: ${client.id}`);
  }

  console.log('');
  console.log('🔗 Test URLs for scraping:');
  console.log('  ✅ Working URLs:');
  console.log('    • https://example.com');
  console.log('    • https://httpbin.org/html');
  console.log('    • https://jsonplaceholder.typicode.com');
  console.log('  ❌ Failing URLs (for error testing):');
  console.log('    • https://httpbin.org/status/404');
  console.log('    • https://httpbin.org/status/500');
  console.log('  ⚠️  Challenging URLs (may require JS):');
  console.log('    • https://react.dev');
  
  console.log('🎉 Database seeding completed successfully!');
}

async function setupTestDatabase() {
  console.log('🚀 Setting up test database...');
  console.log('=====================================');
  
  try {
    // Test database connection
    await pool.query('SELECT 1');
    console.log('✅ Database connection established');
    
    // Run migrations
    await runMigrations();
    
    // Clear existing data
    await clearExistingData();
    
    // Seed with test data
    await seedDatabase();
    
    console.log('');
    console.log('🎉 Test database setup completed successfully!');
    console.log('=====================================');
    console.log('');
    console.log('🧪 Ready for testing scraping functionality!');
    console.log('');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupTestDatabase();
}

export { setupTestDatabase };
