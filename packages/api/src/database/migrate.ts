/**
 * Database migration runner for the AI Answer Bot system.
 * This script executes SQL migrations to set up the PostgreSQL database schema,
 * including tables, indexes, triggers, and initial data for the bot system.
 */

import fs from 'fs';
import path from 'path';
import db from './connection';

const __dirname = path.dirname(__filename);

async function runMigrations() {
  console.log('🚀 Starting database migrations...');

  try {
    // Test database connection
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // Read and execute schema.sql
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');

    console.log('📋 Executing schema migrations...');
    await db.query(schemaSql);

    console.log('✅ Database migrations completed successfully!');
    
    // Verify tables were created
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `;
    
    const result = await db.query(tablesQuery);
    console.log('📊 Created tables:', result.rows.map(row => row.table_name).join(', '));

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}

export { runMigrations };
