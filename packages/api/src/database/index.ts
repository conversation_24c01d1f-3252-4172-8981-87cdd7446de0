/**
 * Database initialization and model registration
 */

import { dbConnection, sequelize } from './connection';
import { Client } from '../models/Client';
import { ScrapedPage } from '../models/ScrapedPage.model';

// Register models with Sequelize
sequelize.addModels([Client, ScrapedPage]);

// Initialize database
export const initializeDatabase = async (options?: { force?: boolean; alter?: boolean }) => {
  try {
    // Test connection
    await dbConnection.authenticate();
    console.log('Database connection established successfully');

    // Sync models
    await dbConnection.sync(options);
    console.log('Database models synchronized successfully');

    return true;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

// Close database connection
export const closeDatabase = async () => {
  try {
    await dbConnection.close();
    console.log('Database connection closed successfully');
  } catch (error) {
    console.error('Failed to close database connection:', error);
    throw error;
  }
};

// Export database connection and models
export { dbConnection, sequelize, Client, ScrapedPage };
export default dbConnection;
