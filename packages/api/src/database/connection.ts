/**
 * Sequelize database connection with TypeScript support.
 * This module provides a Sequelize ORM connection with proper TypeScript decorators.
 */

import { Sequelize } from 'sequelize-typescript';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

class DatabaseConnection {
  private sequelize: Sequelize;
  private static instance: DatabaseConnection;

  private constructor() {
    this.sequelize = new Sequelize({
      dialect: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'ai_answer_bot',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',

      // Connection pool configuration
      pool: {
        max: 20,
        min: 0,
        idle: 10000,
        acquire: 30000,
      },

      // Model configuration
      models: [path.join(__dirname, '../models/**/*.ts')],
      modelMatch: (filename, member) => {
        return filename.substring(0, filename.indexOf('.model')) === member.toLowerCase();
      },

      // Logging configuration
      logging: process.env.NODE_ENV === 'development' ? console.log : false,

      // Additional options
      define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
      },

      // Timezone configuration
      timezone: '+00:00',
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.sequelize.addHook('afterConnect', () => {
      console.log('Connected to PostgreSQL database via Sequelize');
    });

    this.sequelize.addHook('beforeDisconnect', () => {
      console.log('Disconnecting from PostgreSQL database');
    });
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public getSequelize(): Sequelize {
    return this.sequelize;
  }

  public async authenticate(): Promise<boolean> {
    try {
      await this.sequelize.authenticate();
      console.log('Database connection test successful');
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  public async sync(options?: { force?: boolean; alter?: boolean }): Promise<void> {
    try {
      await this.sequelize.sync(options);
      console.log('Database synchronized successfully');
    } catch (error) {
      console.error('Database synchronization failed:', error);
      throw error;
    }
  }

  public async close(): Promise<void> {
    await this.sequelize.close();
  }

  // Transaction support
  public async transaction<T>(callback: (transaction: any) => Promise<T>): Promise<T> {
    return await this.sequelize.transaction(callback);
  }

  // Query interface for raw queries if needed
  public async query(sql: string, options?: any): Promise<any> {
    return await this.sequelize.query(sql, options);
  }
}

export const dbConnection = DatabaseConnection.getInstance();
export const sequelize = dbConnection.getSequelize();
export default dbConnection;
