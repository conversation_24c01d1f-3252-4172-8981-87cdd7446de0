/**
 * Scraper service for web content extraction.
 * This service handles web scraping operations including content extraction,
 * metadata parsing, error handling, and content processing with multiple strategies.
 */

import * as cheerio from 'cheerio';
import puppeteer from 'puppeteer';
import { JSDOM } from 'jsdom';
import { Readability } from '@mozilla/readability';
import urlParse from 'url-parse';
import { lookup } from 'mime-types';
import { ScrapedPageModel, CreateScrapedPageData } from '../models/ScrapedPageModel';
import { ScrapedPage, ScrapeOptions, ScrapeResult } from '../types';

export interface ScrapedContent {
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  language?: string;
  metadata: Record<string, any>;
}

export interface ScrapingError {
  code: string;
  message: string;
  url: string;
  statusCode?: number;
}

export class ScraperService {
  private static readonly DEFAULT_USER_AGENT = 'Mozilla/5.0 (compatible; AI-Answer-Bot/1.0)';
  private static readonly DEFAULT_TIMEOUT = 30000;
  private static readonly MAX_CONTENT_LENGTH = 1000000; // 1MB
  private static readonly BLOCKED_EXTENSIONS = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar'];

  static async scrapeUrl(
    clientId: string,
    url: string,
    options: ScrapeOptions = {}
  ): Promise<ScrapedPage> {
    try {
      // Validate URL
      this.validateUrl(url);

      // Check if page already exists
      const existingPage = await ScrapedPageModel.findByUrl(clientId, url);
      
      // Extract content
      const scrapedContent = await this.extractContent(url, options);
      
      const pageData: CreateScrapedPageData = {
        clientId,
        url,
        title: scrapedContent.title,
        content: scrapedContent.content,
        description: scrapedContent.description,
        thumbnail: scrapedContent.thumbnail,
        language: scrapedContent.language,
        metadata: scrapedContent.metadata,
        scrapingStatus: 'success'
      };

      if (existingPage) {
        // Update existing page
        const updatedPage = await ScrapedPageModel.update(existingPage.id, {
          title: pageData.title,
          content: pageData.content,
          description: pageData.description,
          thumbnail: pageData.thumbnail,
          language: pageData.language,
          metadata: pageData.metadata,
          scrapingStatus: 'success',
          errorMessage: undefined,
          isActive: true
        });
        
        if (!updatedPage) {
          throw new Error('Failed to update existing page');
        }
        
        return updatedPage;
      } else {
        // Create new page
        return await ScrapedPageModel.create(pageData);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown scraping error';
      
      // Try to save/update with error status
      const existingPage = await ScrapedPageModel.findByUrl(clientId, url);
      
      if (existingPage) {
        const updatedPage = await ScrapedPageModel.update(existingPage.id, {
          scrapingStatus: 'failed',
          errorMessage,
          isActive: false
        });
        
        if (updatedPage) {
          return updatedPage;
        }
      }
      
      // Create new page with error status
      const errorPageData: CreateScrapedPageData = {
        clientId,
        url,
        title: 'Failed to scrape',
        content: '',
        scrapingStatus: 'failed',
        errorMessage
      };
      
      return await ScrapedPageModel.create(errorPageData);
    }
  }

  static async bulkScrape(
    clientId: string,
    urls: string[],
    options: ScrapeOptions = {}
  ): Promise<ScrapeResult[]> {
    const results: ScrapeResult[] = [];
    
    // Process URLs in batches to avoid overwhelming the system
    const batchSize = 5;
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (url) => {
        try {
          const page = await this.scrapeUrl(clientId, url, options);
          return {
            url,
            success: page.scrapingStatus === 'success',
            page,
            error: page.errorMessage
          };
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            url: batch[index],
            success: false,
            error: result.reason?.message || 'Promise rejected'
          });
        }
      });
      
      // Add delay between batches to be respectful
      if (i + batchSize < urls.length) {
        await this.delay(1000);
      }
    }
    
    return results;
  }

  private static async extractContent(url: string, options: ScrapeOptions): Promise<ScrapedContent> {
    const {
      waitForJs = false,
      timeout = this.DEFAULT_TIMEOUT,
      userAgent = this.DEFAULT_USER_AGENT,
      followRedirects = true
    } = options;

    if (waitForJs) {
      return await this.extractWithPuppeteer(url, { timeout, userAgent });
    } else {
      return await this.extractWithFetch(url, { timeout, userAgent, followRedirects });
    }
  }

  private static async extractWithFetch(
    url: string,
    options: { timeout: number; userAgent: string; followRedirects: boolean }
  ): Promise<ScrapedContent> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout);

    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': options.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        signal: controller.signal,
        redirect: options.followRedirects ? 'follow' : 'manual'
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('text/html')) {
        throw new Error(`Unsupported content type: ${contentType}`);
      }

      const html = await response.text();
      
      if (html.length > this.MAX_CONTENT_LENGTH) {
        throw new Error(`Content too large: ${html.length} bytes`);
      }

      return this.parseHtmlContent(html, url);
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${options.timeout}ms`);
      }
      
      throw error;
    }
  }

  private static async extractWithPuppeteer(
    url: string,
    options: { timeout: number; userAgent: string }
  ): Promise<ScrapedContent> {
    let browser;
    
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
      });
      
      const page = await browser.newPage();
      await page.setUserAgent(options.userAgent);
      
      // Set viewport and other page settings
      await page.setViewport({ width: 1280, height: 720 });
      
      // Navigate to page
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: options.timeout
      });
      
      // Get page content
      const html = await page.content();
      const title = await page.title();
      
      // Extract additional metadata
      const metadata = await page.evaluate(() => {
        const meta: Record<string, any> = {};
        
        // Get meta tags
        document.querySelectorAll('meta').forEach(tag => {
          const name = tag.getAttribute('name') || tag.getAttribute('property');
          const content = tag.getAttribute('content');
          if (name && content) {
            meta[name] = content;
          }
        });
        
        return meta;
      });
      
      const content = this.parseHtmlContent(html, url);
      
      return {
        ...content,
        title: title || content.title,
        metadata: { ...content.metadata, ...metadata }
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  private static parseHtmlContent(html: string, url: string): ScrapedContent {
    const $ = cheerio.load(html);
    
    // Remove unwanted elements
    $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();
    
    // Extract basic metadata
    const title = $('title').text().trim() || 
                  $('meta[property="og:title"]').attr('content') || 
                  $('h1').first().text().trim() || 
                  'Untitled Page';
    
    const description = $('meta[name="description"]').attr('content') || 
                       $('meta[property="og:description"]').attr('content') || 
                       '';
    
    const thumbnail = $('meta[property="og:image"]').attr('content') || 
                     $('meta[name="twitter:image"]').attr('content') || 
                     '';
    
    const language = $('html').attr('lang') || 
                    $('meta[http-equiv="content-language"]').attr('content') || 
                    '';

    // Use Readability for content extraction
    const dom = new JSDOM(html, { url });
    const reader = new Readability(dom.window.document);
    const article = reader.parse();
    
    let content = '';
    if (article) {
      content = article.textContent || '';
    } else {
      // Fallback to manual extraction
      content = $('main, article, .content, .post, .entry, body').first().text() || 
                $('body').text();
    }
    
    // Clean up content
    content = content
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
    
    // Extract additional metadata
    const metadata: Record<string, any> = {
      url,
      scrapedAt: new Date().toISOString(),
      contentLength: content.length,
      hasImages: $('img').length > 0,
      hasLinks: $('a').length > 0,
      headingCount: $('h1, h2, h3, h4, h5, h6').length
    };
    
    // Add structured data if available
    $('script[type="application/ld+json"]').each((_, element) => {
      try {
        const structuredData = JSON.parse($(element).html() || '');
        metadata.structuredData = structuredData;
      } catch (e) {
        // Ignore invalid JSON
      }
    });

    return {
      title: title.substring(0, 500), // Limit title length
      content,
      description: description.substring(0, 1000), // Limit description length
      thumbnail: this.resolveUrl(thumbnail, url),
      language: language.substring(0, 10), // Limit language code length
      metadata
    };
  }

  private static validateUrl(url: string): void {
    try {
      const parsed = urlParse(url);
      
      if (!parsed.protocol || !parsed.hostname) {
        throw new Error('Invalid URL format');
      }
      
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        throw new Error('Only HTTP and HTTPS URLs are supported');
      }
      
      // Check for blocked file extensions
      const pathname = parsed.pathname.toLowerCase();
      if (this.BLOCKED_EXTENSIONS.some(ext => pathname.endsWith(ext))) {
        throw new Error(`File type not supported: ${pathname}`);
      }
      
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Invalid URL');
    }
  }

  private static resolveUrl(relativeUrl: string, baseUrl: string): string {
    if (!relativeUrl) return '';
    
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
