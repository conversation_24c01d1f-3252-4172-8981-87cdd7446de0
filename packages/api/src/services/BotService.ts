/**
 * Bot service for handling AI responses and content processing.
 * This service manages bot interactions, generates responses based on scraped content,
 * and provides suggestions and link previews for enhanced user experience.
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import db from '../database/connection';
import { BotResponse, BotSuggestion, LinkPreview, ScrapedPage } from '../types';

export class BotService {
  static async generateResponse(question: string, clientId: string): Promise<BotResponse> {
    const questionHash = this.hashQuestion(question);
    
    // Check for cached response
    const cachedResponse = await this.getCachedResponse(clientId, questionHash);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Generate new response
    const response = await this.createNewResponse(question, clientId);
    
    // Cache the response
    await this.cacheResponse(clientId, questionHash, question, response);
    
    return response;
  }

  static async getSuggestions(clientId: string, limit: number = 5): Promise<BotSuggestion[]> {
    const query = `
      SELECT * FROM bot_suggestions 
      WHERE client_id = $1 AND is_active = true
      ORDER BY priority DESC, created_at ASC
      LIMIT $2
    `;
    
    const result = await db.query(query, [clientId, limit]);
    
    return result.rows.map(row => ({
      id: row.id,
      clientId: row.client_id,
      question: row.question,
      category: row.category,
      priority: row.priority,
      isActive: row.is_active
    }));
  }

  static async getLinkPreviews(clientId: string, limit: number = 3): Promise<LinkPreview[]> {
    const query = `
      SELECT id, url, title, description, thumbnail 
      FROM scraped_pages 
      WHERE client_id = $1 AND is_active = true
      ORDER BY last_scraped DESC
      LIMIT $2
    `;
    
    const result = await db.query(query, [clientId, limit]);
    
    return result.rows.map(row => ({
      id: row.id,
      url: row.url,
      title: row.title || 'Untitled Page',
      description: row.description || 'No description available',
      thumbnail: row.thumbnail
    }));
  }

  private static async createNewResponse(question: string, clientId: string): Promise<BotResponse> {
    // Get relevant content from scraped pages
    const relevantContent = await this.findRelevantContent(question, clientId);
    
    // Generate AI response (placeholder - integrate with actual AI service)
    const answer = await this.generateAIResponse(question, relevantContent);
    
    // Get suggestions and link previews
    const [suggestions, linkPreviews] = await Promise.all([
      this.getSuggestions(clientId, 3),
      this.getLinkPreviews(clientId, 2)
    ]);

    return {
      id: uuidv4(),
      question,
      answer,
      suggestions,
      linkPreviews,
      timestamp: new Date()
    };
  }

  private static async findRelevantContent(question: string, clientId: string): Promise<ScrapedPage[]> {
    // Simple keyword matching - in production, use vector search or full-text search
    const keywords = question.toLowerCase().split(' ').filter(word => word.length > 3);
    
    if (keywords.length === 0) {
      return [];
    }

    const keywordConditions = keywords.map((_, index) => 
      `(LOWER(title) LIKE $${index + 2} OR LOWER(content) LIKE $${index + 2})`
    ).join(' OR ');

    const query = `
      SELECT * FROM scraped_pages 
      WHERE client_id = $1 AND is_active = true AND (${keywordConditions})
      ORDER BY last_scraped DESC
      LIMIT 5
    `;

    const params = [clientId, ...keywords.map(keyword => `%${keyword}%`)];
    const result = await db.query(query, params);
    
    return result.rows.map(row => ({
      id: row.id,
      clientId: row.client_id,
      url: row.url,
      title: row.title,
      content: row.content,
      description: row.description,
      thumbnail: row.thumbnail,
      lastScraped: row.last_scraped,
      isActive: row.is_active
    }));
  }

  private static async generateAIResponse(question: string, context: ScrapedPage[]): Promise<string> {
    // Placeholder AI response generation
    // In production, integrate with OpenAI, Claude, or other AI services
    
    if (context.length === 0) {
      return "I'd be happy to help! However, I don't have specific information about that topic in our knowledge base. Could you try rephrasing your question or check out some of the suggested links below?";
    }

    // Simple context-based response
    const contextText = context.map(page => `${page.title}: ${page.description || page.content?.substring(0, 200)}`).join('\n');
    
    return `Based on the information I have, here's what I can tell you about your question:\n\n${contextText.substring(0, 500)}...\n\nWould you like me to provide more specific information about any of these topics?`;
  }

  private static async getCachedResponse(clientId: string, questionHash: string): Promise<BotResponse | null> {
    const query = `
      SELECT * FROM bot_responses 
      WHERE client_id = $1 AND question_hash = $2 AND expires_at > NOW()
    `;
    
    const result = await db.query(query, [clientId, questionHash]);
    
    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return {
      id: row.id,
      question: row.question,
      answer: row.answer,
      suggestions: JSON.parse(row.suggestions),
      linkPreviews: JSON.parse(row.link_previews),
      timestamp: row.created_at
    };
  }

  private static async cacheResponse(
    clientId: string, 
    questionHash: string, 
    question: string, 
    response: BotResponse
  ): Promise<void> {
    const query = `
      INSERT INTO bot_responses (client_id, question_hash, question, answer, suggestions, link_previews)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (client_id, question_hash) 
      DO UPDATE SET 
        answer = EXCLUDED.answer,
        suggestions = EXCLUDED.suggestions,
        link_previews = EXCLUDED.link_previews,
        created_at = NOW(),
        expires_at = NOW() + INTERVAL '24 hours'
    `;

    await db.query(query, [
      clientId,
      questionHash,
      question,
      response.answer,
      JSON.stringify(response.suggestions),
      JSON.stringify(response.linkPreviews)
    ]);
  }

  private static hashQuestion(question: string): string {
    return crypto.createHash('sha256').update(question.toLowerCase().trim()).digest('hex');
  }
}
