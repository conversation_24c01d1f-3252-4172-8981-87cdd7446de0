/**
 * ScrapedPageModel using raw SQL queries
 * This provides the interface expected by the routes while avoiding Sequelize decorator issues
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import db from '../database/connection';
import { ScrapedPage, DatabaseScrapedPage } from '../types';

export interface CreateScrapedPageData {
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
}

export interface UpdateScrapedPageData {
  title?: string;
  content?: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
  isActive?: boolean;
}

export interface SearchOptions {
  query?: string;
  clientId?: string;
  isActive?: boolean;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  language?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'lastScraped' | 'createdAt' | 'title' | 'wordCount';
  sortOrder?: 'ASC' | 'DESC';
}

export class ScrapedPageModel {
  static async create(data: CreateScrapedPageData): Promise<ScrapedPage> {
    const id = uuidv4();
    const contentHash = this.generateContentHash(data.content);
    const wordCount = this.calculateWordCount(data.content);
    const metadata = JSON.stringify(data.metadata || {});

    const query = `
      INSERT INTO scraped_pages (
        id, client_id, url, title, content, description, thumbnail,
        scraping_status, error_message, content_hash, word_count, 
        language, metadata, last_scraped
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW())
      RETURNING *
    `;

    const values = [
      id,
      data.clientId,
      data.url,
      data.title,
      data.content,
      data.description,
      data.thumbnail,
      data.scrapingStatus || 'success',
      data.errorMessage,
      contentHash,
      wordCount,
      data.language,
      metadata
    ];

    const result = await db.query(query, values);
    return this.mapDatabaseToScrapedPage(result.rows[0]);
  }

  static async findById(id: string): Promise<ScrapedPage | null> {
    const query = 'SELECT * FROM scraped_pages WHERE id = $1';
    const result = await db.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToScrapedPage(result.rows[0]);
  }

  static async findByUrl(clientId: string, url: string): Promise<ScrapedPage | null> {
    const query = 'SELECT * FROM scraped_pages WHERE client_id = $1 AND url = $2';
    const result = await db.query(query, [clientId, url]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToScrapedPage(result.rows[0]);
  }

  static async search(options: SearchOptions): Promise<{ pages: ScrapedPage[]; total: number }> {
    const {
      query: searchQuery,
      clientId,
      isActive = true,
      scrapingStatus,
      language,
      limit = 50,
      offset = 0,
      sortBy = 'lastScraped',
      sortOrder = 'DESC'
    } = options;

    let whereClause = 'WHERE 1=1';
    const values: any[] = [];
    let paramCount = 0;

    if (clientId) {
      paramCount++;
      whereClause += ` AND client_id = $${paramCount}`;
      values.push(clientId);
    }

    if (isActive !== undefined) {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      values.push(isActive);
    }

    if (scrapingStatus) {
      paramCount++;
      whereClause += ` AND scraping_status = $${paramCount}`;
      values.push(scrapingStatus);
    }

    if (language) {
      paramCount++;
      whereClause += ` AND language = $${paramCount}`;
      values.push(language);
    }

    if (searchQuery) {
      paramCount++;
      whereClause += ` AND (
        LOWER(title) LIKE LOWER($${paramCount}) OR 
        LOWER(content) LIKE LOWER($${paramCount}) OR 
        LOWER(description) LIKE LOWER($${paramCount})
      )`;
      values.push(`%${searchQuery}%`);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM scraped_pages ${whereClause}`;
    const countResult = await db.query(countQuery, values);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const sortColumn = this.getSortColumn(sortBy);
    const dataQuery = `
      SELECT * FROM scraped_pages ${whereClause}
      ORDER BY ${sortColumn} ${sortOrder}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    values.push(limit, offset);
    const dataResult = await db.query(dataQuery, values);

    return {
      pages: dataResult.rows.map(row => this.mapDatabaseToScrapedPage(row)),
      total
    };
  }

  static async update(id: string, data: UpdateScrapedPageData): Promise<ScrapedPage | null> {
    const updates: string[] = [];
    const values: any[] = [];
    let paramCount = 0;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        const dbColumn = this.getDbColumnName(key);
        updates.push(`${dbColumn} = $${paramCount}`);
        
        if (key === 'metadata' && typeof value === 'object') {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
      }
    });

    if (updates.length === 0) {
      return this.findById(id);
    }

    // Update content hash and word count if content changed
    if (data.content) {
      paramCount++;
      updates.push(`content_hash = $${paramCount}`);
      values.push(this.generateContentHash(data.content));

      paramCount++;
      updates.push(`word_count = $${paramCount}`);
      values.push(this.calculateWordCount(data.content));
    }

    // Always update last_scraped and updated_at
    paramCount++;
    updates.push(`last_scraped = $${paramCount}`);
    values.push(new Date());

    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    paramCount++;
    const query = `
      UPDATE scraped_pages 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;
    values.push(id);

    const result = await db.query(query, values);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToScrapedPage(result.rows[0]);
  }

  static async delete(id: string): Promise<boolean> {
    const query = 'DELETE FROM scraped_pages WHERE id = $1';
    const result = await db.query(query, [id]);
    
    return result.rowCount > 0;
  }

  static async getContentStats(clientId: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    byStatus: Record<string, number>;
    byLanguage: Record<string, number>;
    totalWords: number;
  }> {
    const queries = [
      // Total counts
      'SELECT COUNT(*) as total FROM scraped_pages WHERE client_id = $1',
      'SELECT COUNT(*) as active FROM scraped_pages WHERE client_id = $1 AND is_active = true',
      'SELECT COUNT(*) as inactive FROM scraped_pages WHERE client_id = $1 AND is_active = false',
      // By status
      'SELECT scraping_status, COUNT(*) as count FROM scraped_pages WHERE client_id = $1 GROUP BY scraping_status',
      // By language
      'SELECT language, COUNT(*) as count FROM scraped_pages WHERE client_id = $1 AND language IS NOT NULL GROUP BY language',
      // Total words
      'SELECT COALESCE(SUM(word_count), 0) as total_words FROM scraped_pages WHERE client_id = $1 AND is_active = true'
    ];

    const results = await Promise.all(
      queries.map(query => db.query(query, [clientId]))
    );

    const byStatus: Record<string, number> = {};
    results[3].rows.forEach(row => {
      byStatus[row.scraping_status] = parseInt(row.count);
    });

    const byLanguage: Record<string, number> = {};
    results[4].rows.forEach(row => {
      byLanguage[row.language] = parseInt(row.count);
    });

    return {
      total: parseInt(results[0].rows[0].total),
      active: parseInt(results[1].rows[0].active),
      inactive: parseInt(results[2].rows[0].inactive),
      byStatus,
      byLanguage,
      totalWords: parseInt(results[5].rows[0].total_words)
    };
  }

  private static generateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private static calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private static getSortColumn(sortBy: string): string {
    const columnMap: Record<string, string> = {
      lastScraped: 'last_scraped',
      createdAt: 'created_at',
      title: 'title',
      wordCount: 'word_count'
    };
    return columnMap[sortBy] || 'last_scraped';
  }

  private static getDbColumnName(key: string): string {
    const columnMap: Record<string, string> = {
      scrapingStatus: 'scraping_status',
      errorMessage: 'error_message',
      isActive: 'is_active'
    };
    return columnMap[key] || key;
  }

  private static mapDatabaseToScrapedPage(dbPage: DatabaseScrapedPage): ScrapedPage {
    return {
      id: dbPage.id,
      clientId: dbPage.client_id,
      url: dbPage.url,
      title: dbPage.title,
      content: dbPage.content,
      description: dbPage.description,
      thumbnail: dbPage.thumbnail,
      lastScraped: dbPage.last_scraped,
      isActive: dbPage.is_active,
      createdAt: dbPage.created_at,
      updatedAt: dbPage.updated_at,
      scrapingStatus: dbPage.scraping_status,
      errorMessage: dbPage.error_message,
      contentHash: dbPage.content_hash,
      wordCount: dbPage.word_count,
      language: dbPage.language,
      metadata: typeof dbPage.metadata === 'string' ? JSON.parse(dbPage.metadata) : dbPage.metadata
    };
  }
}
