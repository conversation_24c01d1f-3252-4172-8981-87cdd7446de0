/**
 * ScrapedPage model using Sequelize with TypeScript decorators
 */

import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  BeforeCreate,
  BeforeUpdate
} from 'sequelize-typescript';
import crypto from 'crypto';
import { ScrapedPage as ScrapedPageInterface } from '../types';

export interface CreateScrapedPageData {
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
}

export interface UpdateScrapedPageData {
  title?: string;
  content?: string;
  description?: string;
  thumbnail?: string;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  language?: string;
  metadata?: Record<string, any>;
  isActive?: boolean;
}

export interface SearchOptions {
  query?: string;
  clientId?: string;
  isActive?: boolean;
  scrapingStatus?: 'pending' | 'success' | 'failed' | 'skipped';
  language?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'lastScraped' | 'createdAt' | 'title' | 'wordCount';
  sortOrder?: 'ASC' | 'DESC';
}

@Table({
  tableName: 'scraped_pages',
  timestamps: true,
  underscored: true,
  indexes: [
    { fields: ['client_id'] },
    { fields: ['url'] },
    { fields: ['is_active'] },
    { fields: ['scraping_status'] },
    { fields: ['language'] },
    { fields: ['content_hash'] },
    { fields: ['last_scraped'] }
  ]
})
export class ScrapedPage extends Model<ScrapedPage> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    field: 'client_id'
  })
  clientId!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
      isUrl: true
    }
  })
  url!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 500]
    }
  })
  title!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  })
  content!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  description?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  thumbnail?: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    field: 'last_scraped'
  })
  lastScraped!: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    field: 'is_active'
  })
  isActive!: boolean;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    field: 'created_at'
  })
  createdAt!: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    field: 'updated_at'
  })
  updatedAt!: Date;

  @Column({
    type: DataType.ENUM('pending', 'success', 'failed', 'skipped'),
    allowNull: false,
    defaultValue: 'success',
    field: 'scraping_status'
  })
  scrapingStatus!: 'pending' | 'success' | 'failed' | 'skipped';

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    field: 'error_message'
  })
  errorMessage?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    field: 'content_hash'
  })
  contentHash?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    field: 'word_count'
  })
  wordCount?: number;

  @Column({
    type: DataType.STRING(10),
    allowNull: true
  })
  language?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {}
  })
  metadata?: Record<string, any>;



  // Hooks
  @BeforeCreate
  static generateContentData(instance: ScrapedPage): void {
    if (instance.content) {
      instance.contentHash = ScrapedPage.generateContentHash(instance.content);
      instance.wordCount = ScrapedPage.calculateWordCount(instance.content);
    }
    instance.lastScraped = new Date();
  }

  @BeforeUpdate
  static updateContentData(instance: ScrapedPage): void {
    if (instance.changed('content') && instance.content) {
      instance.contentHash = ScrapedPage.generateContentHash(instance.content);
      instance.wordCount = ScrapedPage.calculateWordCount(instance.content);
    }
    instance.lastScraped = new Date();
  }

  // Static methods for backward compatibility
  static async createPage(data: CreateScrapedPageData): Promise<ScrapedPage> {
    const page = new ScrapedPage();
    page.clientId = data.clientId;
    page.url = data.url;
    page.title = data.title;
    page.content = data.content;
    if (data.description !== undefined) page.description = data.description;
    if (data.thumbnail !== undefined) page.thumbnail = data.thumbnail;
    page.scrapingStatus = data.scrapingStatus || 'success';
    if (data.errorMessage !== undefined) page.errorMessage = data.errorMessage;
    if (data.language !== undefined) page.language = data.language;
    page.metadata = data.metadata || {};
    return await page.save();
  }

  static async findByUrl(clientId: string, url: string): Promise<ScrapedPage | null> {
    return await ScrapedPage.findOne({
      where: { clientId, url }
    });
  }

  // Utility methods
  private static generateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private static calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  // Instance method to convert to interface format
  toScrapedPageInterface(): ScrapedPageInterface {
    return {
      id: this.id,
      clientId: this.clientId,
      url: this.url,
      title: this.title,
      content: this.content,
      description: this.description,
      thumbnail: this.thumbnail,
      lastScraped: this.lastScraped,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      scrapingStatus: this.scrapingStatus,
      errorMessage: this.errorMessage,
      contentHash: this.contentHash,
      wordCount: this.wordCount,
      language: this.language,
      metadata: this.metadata
    };
  }
}
