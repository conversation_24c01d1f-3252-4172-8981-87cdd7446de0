/**
 * Simple Client model using raw SQL queries
 * This avoids Sequelize decorator issues while providing the same interface
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import db from '../database/connection';
import { Client as ClientInterface, ClientConfig, DatabaseClient, CreateClientRequest, UpdateClientRequest } from '../types';

export class Client {
  // Static methods for backward compatibility
  static async create(data: CreateClientRequest): Promise<ClientInterface> {
    const id = uuidv4();
    const apiKey = this.generateApiKey();
    
    const query = `
      INSERT INTO clients (id, name, domain, api_key, config)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;
    
    const values = [
      id,
      data.name,
      data.domain,
      apiKey,
      JSON.stringify(data.config || {})
    ];
    
    const result = await db.query(query, values);
    return this.mapDatabaseToClient(result.rows[0]);
  }

  static async findById(id: string): Promise<ClientInterface | null> {
    const query = 'SELECT * FROM clients WHERE id = $1';
    const result = await db.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToClient(result.rows[0]);
  }

  static async findByApiKey(apiKey: string): Promise<ClientInterface | null> {
    const query = 'SELECT * FROM clients WHERE api_key = $1';
    const result = await db.query(query, [apiKey]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToClient(result.rows[0]);
  }

  static async findByDomain(domain: string): Promise<ClientInterface | null> {
    const query = 'SELECT * FROM clients WHERE domain = $1';
    const result = await db.query(query, [domain]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToClient(result.rows[0]);
  }

  static async findAllClients(limit: number = 50, offset: number = 0): Promise<ClientInterface[]> {
    const query = `
      SELECT * FROM clients 
      ORDER BY created_at DESC 
      LIMIT $1 OFFSET $2
    `;
    const result = await db.query(query, [limit, offset]);
    
    return result.rows.map(row => this.mapDatabaseToClient(row));
  }

  static async update(id: string, data: UpdateClientRequest): Promise<ClientInterface | null> {
    const updates: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(data.name);
    }

    if (data.domain !== undefined) {
      updates.push(`domain = $${paramCount++}`);
      values.push(data.domain);
    }

    if (data.config !== undefined) {
      updates.push(`config = $${paramCount++}`);
      values.push(JSON.stringify(data.config));
    }

    if (updates.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    const query = `
      UPDATE clients 
      SET ${updates.join(', ')}, updated_at = NOW()
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await db.query(query, values);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDatabaseToClient(result.rows[0]);
  }

  static async delete(id: string): Promise<boolean> {
    const query = 'DELETE FROM clients WHERE id = $1';
    const result = await db.query(query, [id]);
    
    return result.rowCount > 0;
  }

  static async regenerateApiKey(id: string): Promise<string | null> {
    const newApiKey = this.generateApiKey();
    
    const query = `
      UPDATE clients 
      SET api_key = $1, updated_at = NOW()
      WHERE id = $2
      RETURNING api_key
    `;
    
    const result = await db.query(query, [newApiKey, id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return result.rows[0].api_key;
  }

  private static generateApiKey(): string {
    return `ak_${crypto.randomBytes(32).toString('hex')}`;
  }

  private static mapDatabaseToClient(dbClient: DatabaseClient): ClientInterface {
    return {
      id: dbClient.id,
      name: dbClient.name,
      domain: dbClient.domain,
      apiKey: dbClient.api_key,
      config: typeof dbClient.config === 'string' 
        ? JSON.parse(dbClient.config) 
        : dbClient.config,
      createdAt: dbClient.created_at,
      updatedAt: dbClient.updated_at
    };
  }

  // Instance method for compatibility
  toClientInterface(): ClientInterface {
    return {
      id: (this as any).id,
      name: (this as any).name,
      domain: (this as any).domain,
      apiKey: (this as any).apiKey,
      config: (this as any).config,
      createdAt: (this as any).createdAt,
      updatedAt: (this as any).updatedAt
    };
  }
}
