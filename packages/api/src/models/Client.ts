/**
 * Client model using Sequelize with TypeScript decorators
 */

import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  CreatedAt,
  UpdatedAt,
  BeforeCreate,
  BeforeUpdate
} from 'sequelize-typescript';

import crypto from 'crypto';
import { Client as ClientInterface, ClientConfig, CreateClientRequest, UpdateClientRequest } from '../types';

@Table({
  tableName: 'clients',
  timestamps: true,
  underscored: true,
})
export class Client extends Model<Client> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  })
  name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      isUrl: true
    }
  })
  domain!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    field: 'api_key'
  })
  apiKey!: string;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    defaultValue: {}
  })
  config!: ClientConfig;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    field: 'created_at'
  })
  createdAt!: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    field: 'updated_at'
  })
  updatedAt!: Date;

  // Hooks
  @BeforeCreate
  static generateApiKeyHook(instance: Client): void {
    if (!instance.apiKey) {
      instance.apiKey = Client.generateApiKey();
    }
  }

  @BeforeUpdate
  static updateTimestamp(instance: Client): void {
    instance.updatedAt = new Date();
  }

  // Static methods for backward compatibility and additional functionality
  static async createClient(data: CreateClientRequest): Promise<Client> {
    const client = new Client();
    client.name = data.name;
    client.domain = data.domain;
    client.config = data.config || {};
    client.apiKey = this.generateApiKey();
    return await client.save();
  }

  static async findByApiKey(apiKey: string): Promise<Client | null> {
    return await Client.findOne({
      where: { apiKey }
    });
  }

  static async findByDomain(domain: string): Promise<Client | null> {
    return await Client.findOne({
      where: { domain }
    });
  }

  static async findAllClients(limit: number = 50, offset: number = 0): Promise<Client[]> {
    return await Client.findAll({
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });
  }

  static async updateClient(id: string, data: UpdateClientRequest): Promise<Client | null> {
    const client = await Client.findByPk(id);
    if (!client) {
      return null;
    }

    const updateData: Partial<Client> = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.domain !== undefined) updateData.domain = data.domain;
    if (data.config !== undefined) updateData.config = data.config;

    await client.update(updateData);
    return client;
  }

  static async deleteClient(id: string): Promise<boolean> {
    const result = await Client.destroy({
      where: { id }
    });
    return result > 0;
  }

  static async regenerateApiKey(id: string): Promise<string | null> {
    const client = await Client.findByPk(id);
    if (!client) {
      return null;
    }

    const newApiKey = this.generateApiKey();
    await client.update({ apiKey: newApiKey });
    return newApiKey;
  }

  // Utility methods
  private static generateApiKey(): string {
    return `ak_${crypto.randomBytes(32).toString('hex')}`;
  }

  // Instance method to convert to interface format
  toClientInterface(): ClientInterface {
    return {
      id: this.id,
      name: this.name,
      domain: this.domain,
      apiKey: this.apiKey,
      config: this.config,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // Instance method to get scraped pages count
  async getScrapedPagesCount(): Promise<number> {
    // This will be implemented when ScrapedPage model is properly set up
    return 0;
  }

  // Instance method to get active scraped pages count
  async getActiveScrapedPagesCount(): Promise<number> {
    // This will be implemented when ScrapedPage model is properly set up
    return 0;
  }
}
