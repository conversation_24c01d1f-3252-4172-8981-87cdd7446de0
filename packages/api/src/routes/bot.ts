/**
 * Bot API routes for handling chat interactions.
 * This module defines Express routes for bot conversations, suggestions, and link previews,
 * with proper validation, authentication, and error handling.
 */

import { Router, Request, Response } from 'express';
import Joi from 'joi';
import { BotService } from '../services/BotService';
import { ClientModel } from '../models/Client';
import { BotAskRequest, BotAskResponse, SuggestionsResponse, LinkPreviewsResponse } from '../types';
import { validateRequest } from '../middleware/validation';
import { authenticateClient } from '../middleware/auth';

const router = Router();

// Validation schemas
const askSchema = Joi.object({
  question: Joi.string().min(1).max(1000).required(),
  clientId: Joi.string().uuid().required()
});

// POST /api/bot/ask - Get bot response to a question
router.post('/ask', validateRequest(askSchema), async (req: Request, res: Response) => {
  try {
    const { question, clientId }: BotAskRequest = req.body;

    // Verify client exists
    const client = await ClientModel.findById(clientId);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Generate bot response
    const response = await BotService.generateResponse(question, clientId);

    const apiResponse: BotAskResponse = {
      id: response.id,
      answer: response.answer,
      suggestions: response.suggestions,
      linkPreviews: response.linkPreviews,
      timestamp: response.timestamp.toISOString()
    };

    res.json(apiResponse);
  } catch (error) {
    console.error('Error in /bot/ask:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/suggestions/:clientId - Get suggestions for a client
router.get('/suggestions/:clientId', async (req: Request, res: Response) => {
  try {
    const { clientId } = req.params;

    // Validate UUID format
    if (!Joi.string().uuid().validate(clientId).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    // Verify client exists
    const client = await ClientModel.findById(clientId);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const suggestions = await BotService.getSuggestions(clientId);

    const response: SuggestionsResponse = {
      suggestions
    };

    res.json(response);
  } catch (error) {
    console.error('Error in /suggestions:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/links/:clientId - Get link previews for a client
router.get('/links/:clientId', async (req: Request, res: Response) => {
  try {
    const { clientId } = req.params;

    // Validate UUID format
    if (!Joi.string().uuid().validate(clientId).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    // Verify client exists
    const client = await ClientModel.findById(clientId);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const links = await BotService.getLinkPreviews(clientId);

    const response: LinkPreviewsResponse = {
      links
    };

    res.json(response);
  } catch (error) {
    console.error('Error in /links:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
