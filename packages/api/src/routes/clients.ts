/**
 * Client API routes for client management and scraping operations.
 * This module defines Express routes for client CRUD operations, scraped page management,
 * and web scraping functionality with proper validation, authentication, and error handling.
 */

import { Router, Request, Response } from 'express';
import Joi from 'joi';
import { Client as ClientModel } from '../models/Client';
import { ScrapedPageModel, SearchOptions } from '../models/ScrapedPageModel';
import { ScraperService } from '../services/ScraperService';
import { 
  CreateClientRequest, 
  UpdateClientRequest, 
  ScrapeUrlRequest,
  BulkScrapeRequest,
  BulkScrapeResponse,
  ScrapeResult
} from '../types';
import { validateRequest } from '../middleware/validation';
import { authenticateAdmin } from '../middleware/auth';

const router = Router();

// Validation schemas
const createClientSchema = Joi.object({
  name: Joi.string().min(1).max(255).required(),
  domain: Joi.string().domain().required(),
  config: Joi.object({
    primaryColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/),
    askAnythingText: Joi.string().max(100),
    logo: Joi.string().uri(),
    placeholder: Joi.string().max(200),
    siteName: Joi.string().max(100)
  }).default({})
});

const updateClientSchema = Joi.object({
  name: Joi.string().min(1).max(255),
  domain: Joi.string().domain(),
  config: Joi.object({
    primaryColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/),
    askAnythingText: Joi.string().max(100),
    logo: Joi.string().uri(),
    placeholder: Joi.string().max(200),
    siteName: Joi.string().max(100)
  })
});

const scrapeUrlSchema = Joi.object({
  url: Joi.string().uri().required(),
  options: Joi.object({
    waitForJs: Joi.boolean().default(false),
    timeout: Joi.number().min(5000).max(60000).default(30000),
    userAgent: Joi.string().max(500),
    followRedirects: Joi.boolean().default(true),
    respectRobots: Joi.boolean().default(true)
  }).default({})
});

const bulkScrapeSchema = Joi.object({
  urls: Joi.array().items(Joi.string().uri()).min(1).max(50).required(),
  options: Joi.object({
    waitForJs: Joi.boolean().default(false),
    timeout: Joi.number().min(5000).max(60000).default(30000),
    userAgent: Joi.string().max(500),
    followRedirects: Joi.boolean().default(true),
    respectRobots: Joi.boolean().default(true)
  }).default({})
});

const searchPagesSchema = Joi.object({
  query: Joi.string().max(500),
  isActive: Joi.boolean(),
  scrapingStatus: Joi.string().valid('pending', 'success', 'failed', 'skipped'),
  language: Joi.string().max(10),
  limit: Joi.number().min(1).max(100).default(50),
  offset: Joi.number().min(0).default(0),
  sortBy: Joi.string().valid('lastScraped', 'createdAt', 'title', 'wordCount').default('lastScraped'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
});

// Client Management Routes

// GET /api/clients - List all clients (admin only)
router.get('/', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const clients = await ClientModel.findAllClients();
    res.json({ clients });
  } catch (error) {
    console.error('Error in GET /clients:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/clients - Create new client (admin only)
router.post('/', authenticateAdmin, validateRequest(createClientSchema), async (req: Request, res: Response) => {
  try {
    const clientData: CreateClientRequest = req.body;
    const client = await ClientModel.create(clientData);
    res.status(201).json(client);
  } catch (error) {
    console.error('Error in POST /clients:', error);
    
    if (error instanceof Error && error.message.includes('duplicate')) {
      res.status(409).json({ error: 'Domain already exists' });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

// GET /api/clients/:id - Get client details
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    const client = await ClientModel.findById(id);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json(client);
  } catch (error) {
    console.error('Error in GET /clients/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/clients/:id - Update client
router.put('/:id', authenticateAdmin, validateRequest(updateClientSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData: UpdateClientRequest = req.body;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    const client = await ClientModel.update(id, updateData);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json(client);
  } catch (error) {
    console.error('Error in PUT /clients/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/clients/:id - Delete client (admin only)
router.delete('/:id', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    const deleted = await ClientModel.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error in DELETE /clients/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/clients/:id/regenerate-key - Regenerate API key (admin only)
router.post('/:id/regenerate-key', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    const apiKey = await ClientModel.regenerateApiKey(id);
    if (!apiKey) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ apiKey });
  } catch (error) {
    console.error('Error in POST /clients/:id/regenerate-key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Scraped Pages Management Routes

// GET /api/clients/:id/pages - Get scraped pages for client
router.get('/:id/pages', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const queryParams = req.query;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    // Validate query parameters
    const { error, value } = searchPagesSchema.validate(queryParams);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const searchOptions: SearchOptions = {
      clientId: id,
      ...value
    };

    const result = await ScrapedPageModel.search(searchOptions);
    
    res.json({
      pages: result.pages,
      pagination: {
        total: result.total,
        limit: value.limit,
        offset: value.offset,
        hasMore: value.offset + value.limit < result.total
      }
    });
  } catch (error) {
    console.error('Error in GET /clients/:id/pages:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/clients/:id/pages/stats - Get content statistics
router.get('/:id/pages/stats', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    const stats = await ScrapedPageModel.getContentStats(id);
    res.json(stats);
  } catch (error) {
    console.error('Error in GET /clients/:id/pages/stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/clients/:id/scrape - Scrape a single URL
router.post('/:id/scrape', validateRequest(scrapeUrlSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { url, options }: ScrapeUrlRequest = req.body;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    // Verify client exists
    const client = await ClientModel.findById(id);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const scrapedPage = await ScraperService.scrapeUrl(id, url, options);
    res.json(scrapedPage);
  } catch (error) {
    console.error('Error in POST /clients/:id/scrape:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/clients/:id/scrape/bulk - Bulk scrape multiple URLs
router.post('/:id/scrape/bulk', validateRequest(bulkScrapeSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { urls, options }: BulkScrapeRequest = req.body;
    
    if (Joi.string().uuid().validate(id).error) {
      return res.status(400).json({ error: 'Invalid client ID format' });
    }

    // Verify client exists
    const client = await ClientModel.findById(id);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const results = await ScraperService.bulkScrape(id, urls, options);
    
    const summary = {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };

    const response: BulkScrapeResponse = {
      results,
      summary
    };

    res.json(response);
  } catch (error) {
    console.error('Error in POST /clients/:id/scrape/bulk:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/clients/:id/pages/:pageId - Update scraped page
router.put('/:id/pages/:pageId', async (req: Request, res: Response) => {
  try {
    const { id, pageId } = req.params;
    const updateData = req.body;
    
    if (Joi.string().uuid().validate(id).error || Joi.string().uuid().validate(pageId).error) {
      return res.status(400).json({ error: 'Invalid ID format' });
    }

    // Verify the page belongs to the client
    const existingPage = await ScrapedPageModel.findById(pageId);
    if (!existingPage || existingPage.clientId !== id) {
      return res.status(404).json({ error: 'Page not found' });
    }

    const updatedPage = await ScrapedPageModel.update(pageId, updateData);
    if (!updatedPage) {
      return res.status(404).json({ error: 'Page not found' });
    }

    res.json(updatedPage);
  } catch (error) {
    console.error('Error in PUT /clients/:id/pages/:pageId:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/clients/:id/pages/:pageId - Delete scraped page
router.delete('/:id/pages/:pageId', async (req: Request, res: Response) => {
  try {
    const { id, pageId } = req.params;
    
    if (Joi.string().uuid().validate(id).error || Joi.string().uuid().validate(pageId).error) {
      return res.status(400).json({ error: 'Invalid ID format' });
    }

    // Verify the page belongs to the client
    const existingPage = await ScrapedPageModel.findById(pageId);
    if (!existingPage || existingPage.clientId !== id) {
      return res.status(404).json({ error: 'Page not found' });
    }

    const deleted = await ScrapedPageModel.delete(pageId);
    if (!deleted) {
      return res.status(404).json({ error: 'Page not found' });
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error in DELETE /clients/:id/pages/:pageId:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
