/**
 * ScrapedPage validation schemas
 */

import <PERSON><PERSON> from 'joi';
import { commonValidators, contentValidators, statusValidators, paginationSchema, validateSchema } from './common.validator';

// Create scraped page request validation schema
export const createScrapedPageSchema = Joi.object({
  clientId: commonValidators.uuid,
  url: commonValidators.url,
  title: contentValidators.title,
  content: contentValidators.content,
  description: contentValidators.description,
  thumbnail: commonValidators.optionalUrl,
  scrapingStatus: statusValidators.optionalScrapingStatus.default('success'),
  errorMessage: Joi.string().max(1000).optional(),
  language: contentValidators.language,
  metadata: contentValidators.metadata
});

// Update scraped page request validation schema
export const updateScrapedPageSchema = Joi.object({
  title: contentValidators.optionalTitle,
  content: contentValidators.optionalContent,
  description: contentValidators.description,
  thumbnail: commonValidators.optionalUrl,
  scrapingStatus: statusValidators.optionalScrapingStatus,
  errorMessage: Joi.string().max(1000).optional(),
  language: contentValidators.language,
  metadata: contentValidators.metadata,
  isActive: commonValidators.optionalBoolean
}).min(1); // At least one field must be provided

// Scraped page search options validation schema
export const scrapedPageSearchSchema = Joi.object({
  query: Joi.string().min(1).max(500).optional(),
  clientId: commonValidators.optionalUuid,
  isActive: commonValidators.optionalBoolean,
  scrapingStatus: statusValidators.optionalScrapingStatus,
  language: contentValidators.language,
  sortBy: Joi.string().valid('lastScraped', 'createdAt', 'title', 'wordCount').default('lastScraped'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
}).concat(paginationSchema);

// Scraped page ID parameter validation schema
export const scrapedPageIdSchema = Joi.object({
  id: commonValidators.uuid
});

// URL and client ID validation schema
export const urlClientSchema = Joi.object({
  clientId: commonValidators.uuid,
  url: commonValidators.url
});

// Bulk scrape request validation schema
export const bulkScrapeSchema = Joi.object({
  clientId: commonValidators.uuid,
  urls: Joi.array().items(commonValidators.url).min(1).max(100).required(),
  options: Joi.object({
    waitForJs: commonValidators.optionalBoolean.default(false),
    timeout: Joi.number().integer().min(1000).max(60000).default(30000),
    userAgent: Joi.string().max(500).optional(),
    followRedirects: commonValidators.optionalBoolean.default(true),
    maxDepth: Joi.number().integer().min(0).max(5).default(0),
    respectRobots: commonValidators.optionalBoolean.default(true)
  }).optional()
});

// Content statistics request validation schema
export const contentStatsSchema = Joi.object({
  clientId: commonValidators.uuid,
  startDate: commonValidators.optionalDate,
  endDate: commonValidators.optionalDate,
  groupBy: Joi.string().valid('day', 'week', 'month').optional()
});

// Scraping options validation schema
export const scrapingOptionsSchema = Joi.object({
  waitForJs: commonValidators.optionalBoolean.default(false),
  timeout: Joi.number().integer().min(1000).max(60000).default(30000),
  userAgent: Joi.string().max(500).optional(),
  followRedirects: commonValidators.optionalBoolean.default(true),
  maxDepth: Joi.number().integer().min(0).max(5).default(0),
  respectRobots: commonValidators.optionalBoolean.default(true),
  extractImages: commonValidators.optionalBoolean.default(false),
  extractLinks: commonValidators.optionalBoolean.default(false)
});

// Single URL scrape request validation schema
export const singleScrapeSchema = Joi.object({
  clientId: commonValidators.uuid,
  url: commonValidators.url,
  options: scrapingOptionsSchema.optional()
});

// Validation functions
export const validateCreateScrapedPage = validateSchema(createScrapedPageSchema);
export const validateUpdateScrapedPage = validateSchema(updateScrapedPageSchema);
export const validateScrapedPageSearch = validateSchema(scrapedPageSearchSchema);
export const validateScrapedPageId = validateSchema(scrapedPageIdSchema);
export const validateUrlClient = validateSchema(urlClientSchema);
export const validateBulkScrape = validateSchema(bulkScrapeSchema);
export const validateContentStats = validateSchema(contentStatsSchema);
export const validateSingleScrape = validateSchema(singleScrapeSchema);
export const validateScrapingOptions = validateSchema(scrapingOptionsSchema);

// Custom validation functions
export const isValidScrapingStatus = (status: string): boolean => {
  return ['pending', 'success', 'failed', 'skipped'].includes(status);
};

export const isValidLanguageCode = (language: string): boolean => {
  // Basic ISO 639-1 language code validation
  return /^[a-z]{2}$/.test(language);
};

export const isContentLengthValid = (content: string, minLength: number = 10, maxLength: number = 1000000): boolean => {
  return content.length >= minLength && content.length <= maxLength;
};

export const isWordCountReasonable = (content: string): boolean => {
  const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
  return wordCount >= 1 && wordCount <= 100000; // Reasonable limits
};

// Content quality validation
export const validateContentQuality = (content: string): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];
  
  if (content.length < 10) {
    issues.push('Content is too short (minimum 10 characters)');
  }
  
  if (content.length > 1000000) {
    issues.push('Content is too long (maximum 1,000,000 characters)');
  }
  
  const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
  if (wordCount < 1) {
    issues.push('Content must contain at least one word');
  }
  
  if (wordCount > 100000) {
    issues.push('Content contains too many words (maximum 100,000 words)');
  }
  
  // Check for suspicious patterns
  const repeatedCharPattern = /(.)\1{50,}/;
  if (repeatedCharPattern.test(content)) {
    issues.push('Content contains suspicious repeated character patterns');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};

// Bulk validation for multiple scraped pages
export const validateBulkScrapedPageData = (pages: any[]): { valid: any[]; invalid: any[] } => {
  const valid: any[] = [];
  const invalid: any[] = [];
  
  pages.forEach((page, index) => {
    const { error, value } = createScrapedPageSchema.validate(page);
    
    if (error) {
      invalid.push({
        index,
        data: page,
        errors: error.details.map(detail => detail.message)
      });
    } else {
      valid.push(value);
    }
  });
  
  return { valid, invalid };
};
