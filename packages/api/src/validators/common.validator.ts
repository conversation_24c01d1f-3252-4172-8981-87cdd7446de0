/**
 * Common validation schemas and utilities
 */

import Joi from 'joi';

// Common validation patterns
export const commonValidators = {
  uuid: Joi.string().uuid().required(),
  optionalUuid: Joi.string().uuid().optional(),
  url: Joi.string().uri().required(),
  optionalUrl: Joi.string().uri().optional(),
  email: Joi.string().email().required(),
  optionalEmail: Joi.string().email().optional(),
  nonEmptyString: Joi.string().min(1).required(),
  optionalNonEmptyString: Joi.string().min(1).optional(),
  positiveInteger: Joi.number().integer().min(1).required(),
  optionalPositiveInteger: Joi.number().integer().min(1).optional(),
  nonNegativeInteger: Joi.number().integer().min(0).required(),
  optionalNonNegativeInteger: Joi.number().integer().min(0).optional(),
  boolean: Joi.boolean().required(),
  optionalBoolean: Joi.boolean().optional(),
  date: Joi.date().required(),
  optionalDate: Joi.date().optional(),
  jsonObject: Joi.object().required(),
  optionalJsonObject: Joi.object().optional()
};

// Pagination validation schema
export const paginationSchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(50),
  offset: Joi.number().integer().min(0).default(0),
  page: Joi.number().integer().min(1).optional(),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
});

// Search query validation schema
export const searchQuerySchema = Joi.object({
  query: Joi.string().min(1).max(500).optional(),
  filters: Joi.object().optional()
}).concat(paginationSchema);

// API Key validation
export const apiKeySchema = Joi.string().pattern(/^ak_[a-f0-9]{64}$/).required();

// Validation error formatter
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export const formatValidationErrors = (error: Joi.ValidationError): ValidationError[] => {
  return error.details.map(detail => ({
    field: detail.path.join('.'),
    message: detail.message,
    value: detail.context?.value
  }));
};

// Validation middleware helper
export const validateSchema = (schema: Joi.ObjectSchema) => {
  return (data: any): { error?: ValidationError[]; value?: any } => {
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      return { error: formatValidationErrors(error) };
    }

    return { value };
  };
};

// Domain validation helper
export const isDomainValid = (domain: string): boolean => {
  const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
  return domainRegex.test(domain);
};

// URL validation helper
export const isUrlValid = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Content validation helpers
export const contentValidators = {
  title: Joi.string().min(1).max(500).required(),
  optionalTitle: Joi.string().min(1).max(500).optional(),
  content: Joi.string().min(1).required(),
  optionalContent: Joi.string().min(1).optional(),
  description: Joi.string().max(1000).optional(),
  language: Joi.string().length(2).optional(), // ISO 639-1 language codes
  metadata: Joi.object().optional()
};

// Status validation
export const statusValidators = {
  scrapingStatus: Joi.string().valid('pending', 'success', 'failed', 'skipped').required(),
  optionalScrapingStatus: Joi.string().valid('pending', 'success', 'failed', 'skipped').optional()
};
