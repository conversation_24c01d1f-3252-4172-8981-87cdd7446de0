/**
 * Client validation schemas
 */

import <PERSON><PERSON> from 'joi';
import { commonValidators, validateSchema } from './common.validator';

// Client configuration validation schema
export const clientConfigSchema = Joi.object({
  primaryColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
  askAnythingText: Joi.string().max(200).optional(),
  logo: commonValidators.optionalUrl,
  placeholder: Joi.string().max(100).optional(),
  siteName: Joi.string().max(100).optional()
});

// Create client request validation schema
export const createClientSchema = Joi.object({
  name: Joi.string().min(1).max(255).required(),
  domain: Joi.string().domain().required(),
  config: clientConfigSchema.optional().default({})
});

// Update client request validation schema
export const updateClientSchema = Joi.object({
  name: Joi.string().min(1).max(255).optional(),
  domain: Joi.string().domain().optional(),
  config: clientConfigSchema.optional()
}).min(1); // At least one field must be provided

// Client query parameters validation schema
export const clientQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(50),
  offset: Joi.number().integer().min(0).default(0),
  search: Joi.string().max(255).optional(),
  sortBy: Joi.string().valid('name', 'domain', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
});

// Client ID parameter validation schema
export const clientIdSchema = Joi.object({
  id: commonValidators.uuid
});

// API key validation schema
export const apiKeyValidationSchema = Joi.object({
  apiKey: Joi.string().pattern(/^ak_[a-f0-9]{64}$/).required()
});

// Domain validation schema
export const domainValidationSchema = Joi.object({
  domain: Joi.string().domain().required()
});

// Regenerate API key request schema
export const regenerateApiKeySchema = Joi.object({
  clientId: commonValidators.uuid
});

// Client statistics request schema
export const clientStatsSchema = Joi.object({
  clientId: commonValidators.uuid,
  startDate: commonValidators.optionalDate,
  endDate: commonValidators.optionalDate
});

// Validation functions
export const validateCreateClient = validateSchema(createClientSchema);
export const validateUpdateClient = validateSchema(updateClientSchema);
export const validateClientQuery = validateSchema(clientQuerySchema);
export const validateClientId = validateSchema(clientIdSchema);
export const validateApiKey = validateSchema(apiKeyValidationSchema);
export const validateDomain = validateSchema(domainValidationSchema);
export const validateRegenerateApiKey = validateSchema(regenerateApiKeySchema);
export const validateClientStats = validateSchema(clientStatsSchema);

// Custom validation functions
export const isValidClientDomain = (domain: string): boolean => {
  // Remove protocol if present
  const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
  
  // Basic domain validation
  const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
  return domainRegex.test(cleanDomain);
};

export const isValidApiKeyFormat = (apiKey: string): boolean => {
  return /^ak_[a-f0-9]{64}$/.test(apiKey);
};

// Client configuration validation helpers
export const validateClientConfig = (config: any): { isValid: boolean; errors: string[] } => {
  const { error } = clientConfigSchema.validate(config);
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message)
    };
  }
  
  return { isValid: true, errors: [] };
};

// Bulk validation for multiple clients
export const validateBulkClientData = (clients: any[]): { valid: any[]; invalid: any[] } => {
  const valid: any[] = [];
  const invalid: any[] = [];
  
  clients.forEach((client, index) => {
    const { error, value } = createClientSchema.validate(client);
    
    if (error) {
      invalid.push({
        index,
        data: client,
        errors: error.details.map(detail => detail.message)
      });
    } else {
      valid.push(value);
    }
  });
  
  return { valid, invalid };
};
