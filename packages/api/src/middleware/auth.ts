/**
 * Authentication middleware for API routes.
 * This module provides authentication functions for validating API keys,
 * client access, and admin permissions with proper error handling.
 */

import { Request, Response, NextFunction } from 'express';
import { ClientModel } from '../models/Client';

export interface AuthenticatedRequest extends Request {
  client?: {
    id: string;
    name: string;
    domain: string;
    apiKey: string;
  };
}

export const authenticateClient = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      return res.status(401).json({ error: 'API key required' });
    }

    const client = await ClientModel.findByApiKey(apiKey);
    
    if (!client) {
      return res.status(401).json({ error: 'Invalid API key' });
    }

    req.client = {
      id: client.id,
      name: client.name,
      domain: client.domain,
      apiKey: client.apiKey
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

export const authenticateAdmin = async (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const adminKey = req.headers['x-admin-key'] as string;
    const expectedAdminKey = process.env.ADMIN_API_KEY;
    
    if (!adminKey || !expectedAdminKey) {
      return res.status(401).json({ error: 'Admin authentication required' });
    }

    if (adminKey !== expectedAdminKey) {
      return res.status(401).json({ error: 'Invalid admin key' });
    }

    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

export const optionalAuth = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (apiKey) {
      const client = await ClientModel.findByApiKey(apiKey);
      if (client) {
        req.client = {
          id: client.id,
          name: client.name,
          domain: client.domain,
          apiKey: client.apiKey
        };
      }
    }

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    // Don't fail the request for optional auth
    next();
  }
};
