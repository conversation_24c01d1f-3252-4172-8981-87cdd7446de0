# @ai-bot/api

The AI Answer Bot API is a Node.js TypeScript backend service that provides intelligent chatbot functionality with PostgreSQL database integration. It handles client management, content scraping, bot responses, and analytics tracking for the embed widget system.

## Features

- **RESTful API**: Clean, well-documented endpoints for all bot functionality
- **PostgreSQL Integration**: Robust database layer with connection pooling
- **Client Management**: Multi-tenant architecture with API key authentication
- **Content Processing**: Website scraping and content analysis capabilities
- **Bot Intelligence**: AI-powered response generation with context awareness
- **Analytics Tracking**: Comprehensive interaction monitoring and reporting
- **Rate Limiting**: Built-in protection against abuse and overuse
- **AWS Ready**: Designed for Lambda deployment with RDS integration

## Installation

```bash
cd packages/api
npm install
```

## Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Key configuration options:
- **Database**: PostgreSQL connection settings
- **Security**: Admin API keys and JWT secrets
- **CORS**: Allowed origins for cross-origin requests
- **AWS**: Deployment and infrastructure settings

## Database Setup

1. Create PostgreSQL database:
```bash
createdb ai_answer_bot
```

2. Run migrations:
```bash
npm run migrate
```

3. Seed initial data (optional):
```bash
npm run seed
```

## Development

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Run production server
npm start

# Run tests
npm test

# Type checking
npm run type-check

# Linting
npm run lint
```

## API Endpoints

### Bot Endpoints
- `POST /api/bot/ask` - Get AI response to user question
- `GET /api/bot/suggestions/:clientId` - Get suggested questions
- `GET /api/bot/links/:clientId` - Get link previews

### Client Management
- `GET /api/clients` - List all clients (admin)
- `POST /api/clients` - Create new client (admin)
- `GET /api/clients/:id` - Get client details
- `PUT /api/clients/:id` - Update client configuration
- `DELETE /api/clients/:id` - Delete client (admin)

### Analytics
- `POST /api/analytics/track` - Track user interaction
- `GET /api/analytics/:clientId` - Get analytics data

### Health Check
- `GET /health` - Server and database health status

## Authentication

The API uses two authentication methods:

1. **Client API Keys**: For embed widget access
   - Header: `X-API-Key: ak_your_client_api_key`

2. **Admin Keys**: For management operations
   - Header: `X-Admin-Key: your_admin_key`

## Database Schema

The system uses PostgreSQL with the following main tables:
- `clients` - Client configuration and API keys
- `scraped_pages` - Website content for each client
- `bot_suggestions` - Predefined questions and responses
- `bot_responses` - Cached AI responses for performance
- `analytics` - User interaction tracking

## Deployment

The API is designed for AWS Lambda deployment:

```bash
# Setup AWS infrastructure
npm run deploy:setup

# Deploy API to Lambda
npm run deploy:api

# Sync database schema
npm run db:migrate
```

## Error Handling

The API provides consistent error responses:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error (server issues)

## Performance

- Connection pooling for database efficiency
- Response caching for frequently asked questions
- Rate limiting to prevent abuse
- Optimized queries with proper indexing
