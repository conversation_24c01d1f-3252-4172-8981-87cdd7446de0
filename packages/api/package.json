{"name": "@ai-bot/api", "version": "1.0.0", "description": "AI Answer Bot API - Node.js TypeScript backend with PostgreSQL for client management and bot functionality", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "migrate": "tsx src/database/migrate.ts", "seed": "tsx src/database/seed.ts", "setup-test-db": "tsx src/database/simple-setup.ts", "test": "jest", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@mozilla/readability": "^0.6.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.1.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "puppeteer": "^24.16.2", "rate-limiter-flexible": "^4.0.1", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "url-parse": "^1.5.10", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.5", "@types/mime-types": "^3.0.1", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/sequelize": "^4.28.20", "@types/url-parse": "^1.4.11", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.0"}}